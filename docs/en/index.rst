ESP RainMaker Programming Guide
===============================
ESP RainMaker is an end-to-end solution offered by Espressif to enable remote control and monitoring for products based on ESP32 series of SoCs (e.g., ESP32, ESP32-S2, ESP32-C3, ESP32-C6, ESP32-C2, etc.) without any configuration required in the Cloud. It provides a device SDK, self-adapting phone apps, transparent cloud middleware and host utilities to reduce complexity in development of connected devices.

This is the C API (for firmware) and Python API (for host tools) documentation for ESP RainMaker. All other documentation can be found at `http://rainmaker.espressif.com <http://rainmaker.espressif.com>`_

.. _C API Reference: c-api-reference/index.html

.. toctree::
   :hidden:

   c-api-reference/index
