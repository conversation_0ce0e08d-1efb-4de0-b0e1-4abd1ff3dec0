# Documentation Source Folder

This folder contains source files of **ESP RainMaker API documentation**.

# Hosted Documentation

* Check the following link for the documentation: https://docs.espressif.com/projects/esp-rainmaker/en/latest/

The above URL is for the master branch latest version.

# Building Documentation

* ESP RainMaker uses esp-docs for building the docs.
* Change to the docs directory and run `build-docs -l en -t esp32`
* To understand more about ESP-Docs, please follow https://docs.espressif.com/projects/esp-docs.
