# ESP RainMaker Username/Password Authentication

This document describes how to use username and password authentication with ESP RainMaker MQTT connections.

## Overview

ESP RainMaker now supports username and password authentication for MQTT connections. This feature allows devices to authenticate with MQTT brokers using traditional username/password credentials instead of or in addition to certificate-based authentication.

## Configuration

### Enable Username/Password Authentication

1. Open menuconfig: `idf.py menuconfig`
2. Navigate to: `Component config` → `ESP RainMaker Common`
3. Enable: `Use Username and Password Authentication`
4. Set the default username and password:
   - `MQTT Username`: Default username for authentication
   - `MQTT Password`: Default password for authentication

### Configuration Options

| Option | Description | Default |
|--------|-------------|---------|
| `CONFIG_ESP_RMAKER_MQTT_USE_USERNAME_PASSWORD` | Enable username/password authentication | `n` |
| `CONFIG_ESP_RMAKER_MQTT_USERNAME` | Default MQTT username | `mqtt_user` |
| `CONFIG_ESP_RMAKER_MQTT_PASSWORD` | Default MQTT password | `mqtt_password` |

## API Functions

### Setting Credentials Programmatically

```c
#include <esp_rmaker_mqtt_glue.h>

// Set MQTT username
esp_err_t esp_rmaker_set_mqtt_username(const char *username);

// Set MQTT password  
esp_err_t esp_rmaker_set_mqtt_password(const char *password);
```

### Getting Credentials

```c
// Get MQTT username (caller must free the returned string)
char *esp_rmaker_get_mqtt_username(void);

// Get MQTT password (caller must free the returned string)
char *esp_rmaker_get_mqtt_password(void);
```

## Usage Example

```c
#include <esp_rmaker_mqtt_glue.h>

void setup_mqtt_auth(void)
{
    // Set custom credentials
    esp_rmaker_set_mqtt_username("my_device_001");
    esp_rmaker_set_mqtt_password("secure_password_123");
    
    // Verify credentials were set
    char *username = esp_rmaker_get_mqtt_username();
    if (username) {
        ESP_LOGI(TAG, "Username: %s", username);
        free(username);
    }
    
    char *password = esp_rmaker_get_mqtt_password();
    if (password) {
        ESP_LOGI(TAG, "Password is set");
        free(password);  // Don't log actual password
    }
}
```

## Authentication Priority

When both username/password and AWS PPI username are configured:

1. **Username/Password Enabled**: Uses the configured username and password
2. **AWS PPI Only**: Uses the AWS PPI string as username (existing behavior)

## Storage

Credentials are stored in NVS (Non-Volatile Storage) in the factory partition:
- Username: `mqtt_username` key
- Password: `mqtt_password` key

## Security Considerations

1. **Password Storage**: Passwords are stored in NVS. Consider using ESP32's flash encryption for additional security.
2. **Transport Security**: Always use TLS/SSL for MQTT connections to protect credentials in transit.
3. **Credential Management**: Implement secure credential provisioning for production devices.

## Compatibility

- Compatible with both ESP-IDF 4.x and 5.x
- Works alongside existing certificate-based authentication
- Backward compatible with existing ESP RainMaker applications

## Troubleshooting

### Common Issues

1. **Authentication Failed**: Verify username and password are correctly set
2. **Feature Not Available**: Ensure `CONFIG_ESP_RMAKER_MQTT_USE_USERNAME_PASSWORD` is enabled
3. **NVS Errors**: Check NVS partition is properly initialized

### Debug Logging

Enable debug logging to see authentication details:
```c
esp_log_level_set("esp_mqtt_glue", ESP_LOG_DEBUG);
```

## Migration Guide

### From Certificate-Only Authentication

1. Enable username/password authentication in menuconfig
2. Set credentials using the API or configuration
3. Test with your MQTT broker
4. Deploy to devices

### Hybrid Authentication

You can use both certificate and username/password authentication simultaneously. The MQTT client will present both credentials to the broker.
