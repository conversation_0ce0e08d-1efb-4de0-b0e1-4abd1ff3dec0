# This is Doxygen configuration file
#
# Doxygen provides over 260 configuration statements
# To make this file easier to follow,
# it contains only statements that are non-default
#
# NOTE:
# It is recommended not to change defaults unless specifically required
# Test any changes how they affect generated documentation
# Make sure that correct warnings are generated to flag issues with documented code
#
# For the complete list of configuration statements see:
# https://www.stack.nl/~dimitri/doxygen/manual/config.html


PROJECT_NAME = "ESP RainMaker Programming Guide"

## The 'INPUT' statement below is used as input by script 'gen-df-input.py'
## to automatically generate API reference list files heder_file.inc
## These files are placed in '_inc' directory
## and used to include in API reference documentation

INPUT = \
    $(PROJECT_PATH)/components/esp_rainmaker/include/esp_rmaker_core.h \
    $(PROJECT_PATH)/components/esp_rainmaker/include/esp_rmaker_user_mapping.h \
    $(PROJECT_PATH)/components/esp_rainmaker/include/esp_rmaker_schedule.h \
    $(PROJECT_PATH)/components/esp_rainmaker/include/esp_rmaker_scenes.h \
    $(PROJECT_PATH)/components/esp_rainmaker/include/esp_rmaker_standard_types.h \
    $(PROJECT_PATH)/components/esp_rainmaker/include/esp_rmaker_standard_params.h \
    $(PROJECT_PATH)/components/esp_rainmaker/include/esp_rmaker_standard_devices.h \
    $(PROJECT_PATH)/components/esp_rainmaker/include/esp_rmaker_standard_services.h \
    $(PROJECT_PATH)/components/esp_rainmaker/include/esp_rmaker_ota.h \
    $(PROJECT_PATH)/components/esp_rainmaker/include/esp_rmaker_mqtt.h \
    $(PROJECT_PATH)/components/esp_rainmaker/include/esp_rmaker_console.h \
    $(PROJECT_PATH)/components/rmaker_common/include/esp_rmaker_common_events.h \
    $(PROJECT_PATH)/components/rmaker_common/include/esp_rmaker_factory.h \
    $(PROJECT_PATH)/components/rmaker_common/include/esp_rmaker_work_queue.h \
    $(PROJECT_PATH)/components/rmaker_common/include/esp_rmaker_utils.h \
    $(PROJECT_PATH)/components/rmaker_common/include/esp_rmaker_cmd_resp.h \
    $(PROJECT_PATH)/components/rmaker_common/include/esp_rmaker_mqtt_glue.h

## Get warnings for functions that have no documentation for their parameters or return value
##
WARN_NO_PARAMDOC = YES

## Enable preprocessing and remove __attribute__(...) expressions from the INPUT files
##
ENABLE_PREPROCESSING   = YES
MACRO_EXPANSION        = YES
EXPAND_ONLY_PREDEF     = YES
PREDEFINED             = \
    $(ENV_DOXYGEN_DEFINES)

## Extract all
EXTRACT_ALL = YES

## Do not complain about not having dot
##
HAVE_DOT = NO

## Generate XML that is required for Breathe
##
GENERATE_XML    = YES
XML_OUTPUT      = xml

GENERATE_HTML   = NO
HAVE_DOT        = NO
GENERATE_LATEX  = NO
GENERATE_MAN    = YES
GENERATE_RTF    = NO

## Skip distracting progress messages
##
QUIET = YES
## Log warnings in a file for further review
##
WARN_LOGFILE = "doxygen-warning-log.txt"
