/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: esp_rmaker_user_mapping.proto */

#ifndef PROTOBUF_C_esp_5frmaker_5fuser_5fmapping_2eproto__INCLUDED
#define PROTOBUF_C_esp_5frmaker_5fuser_5fmapping_2eproto__INCLUDED

#include <protobuf-c/protobuf-c.h>

PROTOBUF_C__BEGIN_DECLS

#if PROTOBUF_C_VERSION_NUMBER < 1003000
# error This file was generated by a newer version of protoc-c which is incompatible with your libprotobuf-c headers. Please update your headers.
#elif 1003003 < PROTOBUF_C_MIN_COMPILER_VERSION
# error This file was generated by an older version of protoc-c which is incompatible with your libprotobuf-c headers. Please regenerate this file with a newer version of protoc-c.
#endif


typedef struct _Rainmaker__CmdSetUserMapping Rainmaker__CmdSetUserMapping;
typedef struct _Rainmaker__RespSetUserMapping Rainmaker__RespSetUserMapping;
typedef struct _Rainmaker__RMakerConfigPayload Rainmaker__RMakerConfigPayload;


/* --- enums --- */

typedef enum _Rainmaker__RMakerConfigStatus {
  RAINMAKER__RMAKER_CONFIG_STATUS__Success = 0,
  RAINMAKER__RMAKER_CONFIG_STATUS__InvalidParam = 1,
  RAINMAKER__RMAKER_CONFIG_STATUS__InvalidState = 2
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(RAINMAKER__RMAKER_CONFIG_STATUS)
} Rainmaker__RMakerConfigStatus;
typedef enum _Rainmaker__RMakerConfigMsgType {
  RAINMAKER__RMAKER_CONFIG_MSG_TYPE__TypeCmdSetUserMapping = 0,
  RAINMAKER__RMAKER_CONFIG_MSG_TYPE__TypeRespSetUserMapping = 1
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(RAINMAKER__RMAKER_CONFIG_MSG_TYPE)
} Rainmaker__RMakerConfigMsgType;

/* --- messages --- */

struct  _Rainmaker__CmdSetUserMapping
{
  ProtobufCMessage base;
  char *userid;
  char *secretkey;
};
#define RAINMAKER__CMD_SET_USER_MAPPING__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rainmaker__cmd_set_user_mapping__descriptor) \
    , (char *)protobuf_c_empty_string, (char *)protobuf_c_empty_string }


struct  _Rainmaker__RespSetUserMapping
{
  ProtobufCMessage base;
  Rainmaker__RMakerConfigStatus status;
  char *nodeid;
};
#define RAINMAKER__RESP_SET_USER_MAPPING__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rainmaker__resp_set_user_mapping__descriptor) \
    , RAINMAKER__RMAKER_CONFIG_STATUS__Success, (char *)protobuf_c_empty_string }


typedef enum {
  RAINMAKER__RMAKER_CONFIG_PAYLOAD__PAYLOAD__NOT_SET = 0,
  RAINMAKER__RMAKER_CONFIG_PAYLOAD__PAYLOAD_CMD_SET_USER_MAPPING = 10,
  RAINMAKER__RMAKER_CONFIG_PAYLOAD__PAYLOAD_RESP_SET_USER_MAPPING = 11
    PROTOBUF_C__FORCE_ENUM_TO_BE_INT_SIZE(RAINMAKER__RMAKER_CONFIG_PAYLOAD__PAYLOAD)
} Rainmaker__RMakerConfigPayload__PayloadCase;

struct  _Rainmaker__RMakerConfigPayload
{
  ProtobufCMessage base;
  Rainmaker__RMakerConfigMsgType msg;
  Rainmaker__RMakerConfigPayload__PayloadCase payload_case;
  union {
    Rainmaker__CmdSetUserMapping *cmd_set_user_mapping;
    Rainmaker__RespSetUserMapping *resp_set_user_mapping;
  };
};
#define RAINMAKER__RMAKER_CONFIG_PAYLOAD__INIT \
 { PROTOBUF_C_MESSAGE_INIT (&rainmaker__rmaker_config_payload__descriptor) \
    , RAINMAKER__RMAKER_CONFIG_MSG_TYPE__TypeCmdSetUserMapping, RAINMAKER__RMAKER_CONFIG_PAYLOAD__PAYLOAD__NOT_SET, {0} }


/* Rainmaker__CmdSetUserMapping methods */
void   rainmaker__cmd_set_user_mapping__init
                     (Rainmaker__CmdSetUserMapping         *message);
size_t rainmaker__cmd_set_user_mapping__get_packed_size
                     (const Rainmaker__CmdSetUserMapping   *message);
size_t rainmaker__cmd_set_user_mapping__pack
                     (const Rainmaker__CmdSetUserMapping   *message,
                      uint8_t             *out);
size_t rainmaker__cmd_set_user_mapping__pack_to_buffer
                     (const Rainmaker__CmdSetUserMapping   *message,
                      ProtobufCBuffer     *buffer);
Rainmaker__CmdSetUserMapping *
       rainmaker__cmd_set_user_mapping__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rainmaker__cmd_set_user_mapping__free_unpacked
                     (Rainmaker__CmdSetUserMapping *message,
                      ProtobufCAllocator *allocator);
/* Rainmaker__RespSetUserMapping methods */
void   rainmaker__resp_set_user_mapping__init
                     (Rainmaker__RespSetUserMapping         *message);
size_t rainmaker__resp_set_user_mapping__get_packed_size
                     (const Rainmaker__RespSetUserMapping   *message);
size_t rainmaker__resp_set_user_mapping__pack
                     (const Rainmaker__RespSetUserMapping   *message,
                      uint8_t             *out);
size_t rainmaker__resp_set_user_mapping__pack_to_buffer
                     (const Rainmaker__RespSetUserMapping   *message,
                      ProtobufCBuffer     *buffer);
Rainmaker__RespSetUserMapping *
       rainmaker__resp_set_user_mapping__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rainmaker__resp_set_user_mapping__free_unpacked
                     (Rainmaker__RespSetUserMapping *message,
                      ProtobufCAllocator *allocator);
/* Rainmaker__RMakerConfigPayload methods */
void   rainmaker__rmaker_config_payload__init
                     (Rainmaker__RMakerConfigPayload         *message);
size_t rainmaker__rmaker_config_payload__get_packed_size
                     (const Rainmaker__RMakerConfigPayload   *message);
size_t rainmaker__rmaker_config_payload__pack
                     (const Rainmaker__RMakerConfigPayload   *message,
                      uint8_t             *out);
size_t rainmaker__rmaker_config_payload__pack_to_buffer
                     (const Rainmaker__RMakerConfigPayload   *message,
                      ProtobufCBuffer     *buffer);
Rainmaker__RMakerConfigPayload *
       rainmaker__rmaker_config_payload__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data);
void   rainmaker__rmaker_config_payload__free_unpacked
                     (Rainmaker__RMakerConfigPayload *message,
                      ProtobufCAllocator *allocator);
/* --- per-message closures --- */

typedef void (*Rainmaker__CmdSetUserMapping_Closure)
                 (const Rainmaker__CmdSetUserMapping *message,
                  void *closure_data);
typedef void (*Rainmaker__RespSetUserMapping_Closure)
                 (const Rainmaker__RespSetUserMapping *message,
                  void *closure_data);
typedef void (*Rainmaker__RMakerConfigPayload_Closure)
                 (const Rainmaker__RMakerConfigPayload *message,
                  void *closure_data);

/* --- services --- */


/* --- descriptors --- */

extern const ProtobufCEnumDescriptor    rainmaker__rmaker_config_status__descriptor;
extern const ProtobufCEnumDescriptor    rainmaker__rmaker_config_msg_type__descriptor;
extern const ProtobufCMessageDescriptor rainmaker__cmd_set_user_mapping__descriptor;
extern const ProtobufCMessageDescriptor rainmaker__resp_set_user_mapping__descriptor;
extern const ProtobufCMessageDescriptor rainmaker__rmaker_config_payload__descriptor;

PROTOBUF_C__END_DECLS


#endif  /* PROTOBUF_C_esp_5frmaker_5fuser_5fmapping_2eproto__INCLUDED */
