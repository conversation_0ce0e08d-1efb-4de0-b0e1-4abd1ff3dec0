/* Generated by the protocol buffer compiler.  DO NOT EDIT! */
/* Generated from: esp_rmaker_claim.proto */

/* Do not generate deprecated warnings for self */
#ifndef PROTOBUF_C__NO_DEPRECATED
#define PROTOBUF_C__NO_DEPRECATED
#endif

#include "esp_rmaker_claim.pb-c.h"
void   rmaker_claim__payload_buf__init
                     (RmakerClaim__PayloadBuf         *message)
{
  static const RmakerClaim__PayloadBuf init_value = RMAKER_CLAIM__PAYLOAD_BUF__INIT;
  *message = init_value;
}
size_t rmaker_claim__payload_buf__get_packed_size
                     (const RmakerClaim__PayloadBuf *message)
{
  assert(message->base.descriptor == &rmaker_claim__payload_buf__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t rmaker_claim__payload_buf__pack
                     (const RmakerClaim__PayloadBuf *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &rmaker_claim__payload_buf__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t rmaker_claim__payload_buf__pack_to_buffer
                     (const RmakerClaim__PayloadBuf *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &rmaker_claim__payload_buf__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
RmakerClaim__PayloadBuf *
       rmaker_claim__payload_buf__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (RmakerClaim__PayloadBuf *)
     protobuf_c_message_unpack (&rmaker_claim__payload_buf__descriptor,
                                allocator, len, data);
}
void   rmaker_claim__payload_buf__free_unpacked
                     (RmakerClaim__PayloadBuf *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &rmaker_claim__payload_buf__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   rmaker_claim__resp_payload__init
                     (RmakerClaim__RespPayload         *message)
{
  static const RmakerClaim__RespPayload init_value = RMAKER_CLAIM__RESP_PAYLOAD__INIT;
  *message = init_value;
}
size_t rmaker_claim__resp_payload__get_packed_size
                     (const RmakerClaim__RespPayload *message)
{
  assert(message->base.descriptor == &rmaker_claim__resp_payload__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t rmaker_claim__resp_payload__pack
                     (const RmakerClaim__RespPayload *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &rmaker_claim__resp_payload__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t rmaker_claim__resp_payload__pack_to_buffer
                     (const RmakerClaim__RespPayload *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &rmaker_claim__resp_payload__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
RmakerClaim__RespPayload *
       rmaker_claim__resp_payload__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (RmakerClaim__RespPayload *)
     protobuf_c_message_unpack (&rmaker_claim__resp_payload__descriptor,
                                allocator, len, data);
}
void   rmaker_claim__resp_payload__free_unpacked
                     (RmakerClaim__RespPayload *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &rmaker_claim__resp_payload__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
void   rmaker_claim__rmaker_claim_payload__init
                     (RmakerClaim__RMakerClaimPayload         *message)
{
  static const RmakerClaim__RMakerClaimPayload init_value = RMAKER_CLAIM__RMAKER_CLAIM_PAYLOAD__INIT;
  *message = init_value;
}
size_t rmaker_claim__rmaker_claim_payload__get_packed_size
                     (const RmakerClaim__RMakerClaimPayload *message)
{
  assert(message->base.descriptor == &rmaker_claim__rmaker_claim_payload__descriptor);
  return protobuf_c_message_get_packed_size ((const ProtobufCMessage*)(message));
}
size_t rmaker_claim__rmaker_claim_payload__pack
                     (const RmakerClaim__RMakerClaimPayload *message,
                      uint8_t       *out)
{
  assert(message->base.descriptor == &rmaker_claim__rmaker_claim_payload__descriptor);
  return protobuf_c_message_pack ((const ProtobufCMessage*)message, out);
}
size_t rmaker_claim__rmaker_claim_payload__pack_to_buffer
                     (const RmakerClaim__RMakerClaimPayload *message,
                      ProtobufCBuffer *buffer)
{
  assert(message->base.descriptor == &rmaker_claim__rmaker_claim_payload__descriptor);
  return protobuf_c_message_pack_to_buffer ((const ProtobufCMessage*)message, buffer);
}
RmakerClaim__RMakerClaimPayload *
       rmaker_claim__rmaker_claim_payload__unpack
                     (ProtobufCAllocator  *allocator,
                      size_t               len,
                      const uint8_t       *data)
{
  return (RmakerClaim__RMakerClaimPayload *)
     protobuf_c_message_unpack (&rmaker_claim__rmaker_claim_payload__descriptor,
                                allocator, len, data);
}
void   rmaker_claim__rmaker_claim_payload__free_unpacked
                     (RmakerClaim__RMakerClaimPayload *message,
                      ProtobufCAllocator *allocator)
{
  if(!message)
    return;
  assert(message->base.descriptor == &rmaker_claim__rmaker_claim_payload__descriptor);
  protobuf_c_message_free_unpacked ((ProtobufCMessage*)message, allocator);
}
static const ProtobufCFieldDescriptor rmaker_claim__payload_buf__field_descriptors[3] =
{
  {
    "Offset",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(RmakerClaim__PayloadBuf, offset),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Payload",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_BYTES,
    0,   /* quantifier_offset */
    offsetof(RmakerClaim__PayloadBuf, payload),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "TotalLen",
    3,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_UINT32,
    0,   /* quantifier_offset */
    offsetof(RmakerClaim__PayloadBuf, totallen),
    NULL,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned rmaker_claim__payload_buf__field_indices_by_name[] = {
  0,   /* field[0] = Offset */
  1,   /* field[1] = Payload */
  2,   /* field[2] = TotalLen */
};
static const ProtobufCIntRange rmaker_claim__payload_buf__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor rmaker_claim__payload_buf__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "rmaker_claim.PayloadBuf",
  "PayloadBuf",
  "RmakerClaim__PayloadBuf",
  "rmaker_claim",
  sizeof(RmakerClaim__PayloadBuf),
  3,
  rmaker_claim__payload_buf__field_descriptors,
  rmaker_claim__payload_buf__field_indices_by_name,
  1,  rmaker_claim__payload_buf__number_ranges,
  (ProtobufCMessageInit) rmaker_claim__payload_buf__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor rmaker_claim__resp_payload__field_descriptors[2] =
{
  {
    "Status",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(RmakerClaim__RespPayload, status),
    &rmaker_claim__rmaker_claim_status__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "Buf",
    2,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    0,   /* quantifier_offset */
    offsetof(RmakerClaim__RespPayload, buf),
    &rmaker_claim__payload_buf__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned rmaker_claim__resp_payload__field_indices_by_name[] = {
  1,   /* field[1] = Buf */
  0,   /* field[0] = Status */
};
static const ProtobufCIntRange rmaker_claim__resp_payload__number_ranges[1 + 1] =
{
  { 1, 0 },
  { 0, 2 }
};
const ProtobufCMessageDescriptor rmaker_claim__resp_payload__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "rmaker_claim.RespPayload",
  "RespPayload",
  "RmakerClaim__RespPayload",
  "rmaker_claim",
  sizeof(RmakerClaim__RespPayload),
  2,
  rmaker_claim__resp_payload__field_descriptors,
  rmaker_claim__resp_payload__field_indices_by_name,
  1,  rmaker_claim__resp_payload__number_ranges,
  (ProtobufCMessageInit) rmaker_claim__resp_payload__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCFieldDescriptor rmaker_claim__rmaker_claim_payload__field_descriptors[3] =
{
  {
    "msg",
    1,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_ENUM,
    0,   /* quantifier_offset */
    offsetof(RmakerClaim__RMakerClaimPayload, msg),
    &rmaker_claim__rmaker_claim_msg_type__descriptor,
    NULL,
    0,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "cmdPayload",
    10,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(RmakerClaim__RMakerClaimPayload, payload_case),
    offsetof(RmakerClaim__RMakerClaimPayload, cmdpayload),
    &rmaker_claim__payload_buf__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
  {
    "respPayload",
    11,
    PROTOBUF_C_LABEL_NONE,
    PROTOBUF_C_TYPE_MESSAGE,
    offsetof(RmakerClaim__RMakerClaimPayload, payload_case),
    offsetof(RmakerClaim__RMakerClaimPayload, resppayload),
    &rmaker_claim__resp_payload__descriptor,
    NULL,
    0 | PROTOBUF_C_FIELD_FLAG_ONEOF,             /* flags */
    0,NULL,NULL    /* reserved1,reserved2, etc */
  },
};
static const unsigned rmaker_claim__rmaker_claim_payload__field_indices_by_name[] = {
  1,   /* field[1] = cmdPayload */
  0,   /* field[0] = msg */
  2,   /* field[2] = respPayload */
};
static const ProtobufCIntRange rmaker_claim__rmaker_claim_payload__number_ranges[2 + 1] =
{
  { 1, 0 },
  { 10, 1 },
  { 0, 3 }
};
const ProtobufCMessageDescriptor rmaker_claim__rmaker_claim_payload__descriptor =
{
  PROTOBUF_C__MESSAGE_DESCRIPTOR_MAGIC,
  "rmaker_claim.RMakerClaimPayload",
  "RMakerClaimPayload",
  "RmakerClaim__RMakerClaimPayload",
  "rmaker_claim",
  sizeof(RmakerClaim__RMakerClaimPayload),
  3,
  rmaker_claim__rmaker_claim_payload__field_descriptors,
  rmaker_claim__rmaker_claim_payload__field_indices_by_name,
  2,  rmaker_claim__rmaker_claim_payload__number_ranges,
  (ProtobufCMessageInit) rmaker_claim__rmaker_claim_payload__init,
  NULL,NULL,NULL    /* reserved[123] */
};
static const ProtobufCEnumValue rmaker_claim__rmaker_claim_status__enum_values_by_number[5] =
{
  { "Success", "RMAKER_CLAIM__RMAKER_CLAIM_STATUS__Success", 0 },
  { "Fail", "RMAKER_CLAIM__RMAKER_CLAIM_STATUS__Fail", 1 },
  { "InvalidParam", "RMAKER_CLAIM__RMAKER_CLAIM_STATUS__InvalidParam", 2 },
  { "InvalidState", "RMAKER_CLAIM__RMAKER_CLAIM_STATUS__InvalidState", 3 },
  { "NoMemory", "RMAKER_CLAIM__RMAKER_CLAIM_STATUS__NoMemory", 4 },
};
static const ProtobufCIntRange rmaker_claim__rmaker_claim_status__value_ranges[] = {
{0, 0},{0, 5}
};
static const ProtobufCEnumValueIndex rmaker_claim__rmaker_claim_status__enum_values_by_name[5] =
{
  { "Fail", 1 },
  { "InvalidParam", 2 },
  { "InvalidState", 3 },
  { "NoMemory", 4 },
  { "Success", 0 },
};
const ProtobufCEnumDescriptor rmaker_claim__rmaker_claim_status__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "rmaker_claim.RMakerClaimStatus",
  "RMakerClaimStatus",
  "RmakerClaim__RMakerClaimStatus",
  "rmaker_claim",
  5,
  rmaker_claim__rmaker_claim_status__enum_values_by_number,
  5,
  rmaker_claim__rmaker_claim_status__enum_values_by_name,
  1,
  rmaker_claim__rmaker_claim_status__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
static const ProtobufCEnumValue rmaker_claim__rmaker_claim_msg_type__enum_values_by_number[8] =
{
  { "TypeCmdClaimStart", "RMAKER_CLAIM__RMAKER_CLAIM_MSG_TYPE__TypeCmdClaimStart", 0 },
  { "TypeRespClaimStart", "RMAKER_CLAIM__RMAKER_CLAIM_MSG_TYPE__TypeRespClaimStart", 1 },
  { "TypeCmdClaimInit", "RMAKER_CLAIM__RMAKER_CLAIM_MSG_TYPE__TypeCmdClaimInit", 2 },
  { "TypeRespClaimInit", "RMAKER_CLAIM__RMAKER_CLAIM_MSG_TYPE__TypeRespClaimInit", 3 },
  { "TypeCmdClaimVerify", "RMAKER_CLAIM__RMAKER_CLAIM_MSG_TYPE__TypeCmdClaimVerify", 4 },
  { "TypeRespClaimVerify", "RMAKER_CLAIM__RMAKER_CLAIM_MSG_TYPE__TypeRespClaimVerify", 5 },
  { "TypeCmdClaimAbort", "RMAKER_CLAIM__RMAKER_CLAIM_MSG_TYPE__TypeCmdClaimAbort", 6 },
  { "TypeRespClaimAbort", "RMAKER_CLAIM__RMAKER_CLAIM_MSG_TYPE__TypeRespClaimAbort", 7 },
};
static const ProtobufCIntRange rmaker_claim__rmaker_claim_msg_type__value_ranges[] = {
{0, 0},{0, 8}
};
static const ProtobufCEnumValueIndex rmaker_claim__rmaker_claim_msg_type__enum_values_by_name[8] =
{
  { "TypeCmdClaimAbort", 6 },
  { "TypeCmdClaimInit", 2 },
  { "TypeCmdClaimStart", 0 },
  { "TypeCmdClaimVerify", 4 },
  { "TypeRespClaimAbort", 7 },
  { "TypeRespClaimInit", 3 },
  { "TypeRespClaimStart", 1 },
  { "TypeRespClaimVerify", 5 },
};
const ProtobufCEnumDescriptor rmaker_claim__rmaker_claim_msg_type__descriptor =
{
  PROTOBUF_C__ENUM_DESCRIPTOR_MAGIC,
  "rmaker_claim.RMakerClaimMsgType",
  "RMakerClaimMsgType",
  "RmakerClaim__RMakerClaimMsgType",
  "rmaker_claim",
  8,
  rmaker_claim__rmaker_claim_msg_type__enum_values_by_number,
  8,
  rmaker_claim__rmaker_claim_msg_type__enum_values_by_name,
  1,
  rmaker_claim__rmaker_claim_msg_type__value_ranges,
  NULL,NULL,NULL,NULL   /* reserved[1234] */
};
