## IDF Component Manager Manifest File
version: "1.6.0"
description: ESP RainMaker firmware agent
url: https://github.com/espressif/esp-rainmaker/tree/master/components/esp_rainmaker
repository: https://github.com/espressif/esp-rainmaker.git
issues: https://github.com/espressif/esp-rainmaker/issues
documentation: https://rainmaker.espressif.com/
discussion: https://www.esp32.com/viewforum.php?f=41
dependencies:
  espressif/mdns:
    version: "^1.2.0"
    rules:
      - if: "idf_version >=5.0"
  espressif/esp_secure_cert_mgr:
    version: "^2.2.1"
    rules:
      - if: "idf_version >=4.3"
  espressif/rmaker_common:
    version: "~1.4.10"
  espressif/json_parser:
    version: "~1.0.3"
  espressif/json_generator:
    version: "~1.1.1"
  espressif/esp_schedule:
    version: "~1.2.0"
  espressif/network_provisioning:
    version: "~1.0.0"
    rules:
      - if: "idf_version >= 5.1"
  espressif/esp_rcp_update:
    # This allows all the minor version updates
    version: "^1.2.0"
    rules:
      - if: "idf_version >= 5.1"
