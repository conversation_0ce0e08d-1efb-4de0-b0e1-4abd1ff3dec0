name: Push components to Espressif Component Registry

on:
  push:
    branches:
      - master

jobs:
  upload_components:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          submodules: "recursive"

      - name: Upload components to component registry
        uses: espressif/upload-components-ci-action@v1
        with:
          namespace: "espressif"
          name: "rmaker_common"
          api_token: ${{ secrets.IDF_COMPONENT_API_TOKEN }}
