stages:
  - build
  - deploy

variables:
  V: "0"
  MAKEFLAGS: "-j8 --no-keep-going"
  APP_BUILD: "all"
  GIT_SUBMODULE_STRATEGY: recursive
  ESP_DOCS_ENV_IMAGE: "$CI_DOCKER_REGISTRY/esp-idf-doc-env-v5.0:2-2"
  ESP_DOCS_PATH: "$CI_PROJECT_DIR"

before_script:
   # add gitlab ssh key
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo -n $GITLAB_KEY > ~/.ssh/id_rsa_base64
    - base64 --decode --ignore-garbage ~/.ssh/id_rsa_base64 > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - echo -e "Host gitlab.espressif.cn\n\tStrictHostKeyChecking no\n" >> ~/.ssh/config
    # Create esp-rainmaker-bins-${CI_JOB_ID}/ dir (to copy binaries into for artifacts)
    - mkdir -p $CI_PROJECT_DIR/esp-rainmaker-bins-${CI_JOB_ID}

.build_matter_examples: &build_matter_examples
  # start building examples
  - for EXAMPLE in $EXAMPLES; do
  - cd $CI_PROJECT_DIR/examples/$EXAMPLE
  - echo Building $EXAMPLE
  # build examples for the targets
  - for TARGET in $EXAMPLE_TARGETS; do
  - echo Building for $TARGET
  - idf.py set-target $TARGET
  - idf.py build
  - mkdir -p $CI_PROJECT_DIR/esp-rainmaker-bins-${CI_JOB_ID}/$EXAMPLE/$TARGET/
  - cp $CI_PROJECT_DIR/examples/$EXAMPLE/build/*.bin $CI_PROJECT_DIR/esp-rainmaker-bins-${CI_JOB_ID}/$EXAMPLE/$TARGET/
  - done
  - echo Build Complete for $EXAMPLE
  - done
  # Generating zip file for binaries generated
  - cd $CI_PROJECT_DIR
  - echo Generating zip file for binaries generated
  - tar -zcvf esp-rainmaker-bins-${CI_JOB_ID}.zip esp-rainmaker-bins-${CI_JOB_ID}/

.build_matter_controller_example: &build_matter_controller_example
  # start build controller example for the targets
  - cd $CI_PROJECT_DIR/examples/matter/matter_controller
  - for TARGET in $CONTROLLER_EXAMPLE_TARGET; do
  - echo Build matter controller for $TARGET
  - idf.py set-target $CONTROLLER_EXAMPLE_TARGET
  - idf.py build
  - mkdir -p $CI_PROJECT_DIR/esp-rainmaker-bins-${CI_JOB_ID}/matter/matter_controller/$TARGET/
  - cp $CI_PROJECT_DIR/examples/matter/matter_controller/build/*.bin $CI_PROJECT_DIR/esp-rainmaker-bins-${CI_JOB_ID}/matter/matter_controller/$TARGET/
  - done
  - echo Build Complete for matter_controller
  # Generating zip file for binaries generated
  - cd $CI_PROJECT_DIR
  - echo Generating zip file for binaries generated
  - tar -zcvf esp-rainmaker-bins-${CI_JOB_ID}.zip esp-rainmaker-bins-${CI_JOB_ID}/

.build_all_examples: &build_all_examples
  - if [[ "$COMPONENT_MGR_142" == "1" ]]; then
  - pip install idf-component-manager==1.4.2
  - else
  - pip install --upgrade idf-component-manager
  - fi
  - for EXAMPLE in $EXAMPLES; do
  - cd $CI_PROJECT_DIR/examples/$EXAMPLE
  - echo Building $EXAMPLE
  - if [[ "$EXAMPLE" == "homekit_switch" ]]; then
  - cd components
  - echo Cloning esp-homekit-sdk
  - git clone --recursive --branch master --depth 1 https://github.com/espressif/esp-homekit-sdk.git
  - cd ..
  - export HOMEKIT_PATH=$PWD/components/esp-homekit-sdk
  - fi
  - if [[ "$EXAMPLE" == "thread_br" ]]; then
  - sed -i "/CONFIG_LWIP_IPV6_NUM_ADDRESSES=8/c\\CONFIG_LWIP_IPV6_NUM_ADDRESSES=12" sdkconfig.defaults
  - fi
  - for TARGET in $EXAMPLE_TARGETS; do
  - echo Building for $TARGET
  - if [[ "$EXAMPLE" == "homekit_switch" && "$TARGET" == "esp32h2" ]]; then
  -   echo "Skipping homekit_switch build for esp32h2"
  -   continue
  - fi
  - rm -rf managed_components dependencies.lock build sdkconfig sdkconfig.old
  - idf.py set-target $TARGET
  - idf.py build
  - mkdir -p $CI_PROJECT_DIR/esp-rainmaker-bins-${CI_JOB_ID}/$EXAMPLE/$TARGET/
  - cp $CI_PROJECT_DIR/examples/$EXAMPLE/build/*.bin $CI_PROJECT_DIR/esp-rainmaker-bins-${CI_JOB_ID}/$EXAMPLE/$TARGET/
  - done
  - echo Build Complete for $EXAMPLE
  - done
  - if [[ "$EXAMPLE" == "thread_br" ]]; then
  - sed -i "/CONFIG_LWIP_IPV6_NUM_ADDRESSES=12/c\\CONFIG_LWIP_IPV6_NUM_ADDRESSES=8" sdkconfig.defaults
  - fi
  # Generating zip file for binaries generated
  - cd $CI_PROJECT_DIR
  - echo Generating zip file for binaries generated
  - tar -zcvf esp-rainmaker-bins-${CI_JOB_ID}.zip esp-rainmaker-bins-${CI_JOB_ID}/

.build_template:
  stage: build
  image: espressif/idf:latest
  tags:
    - build
  variables:
    PEDANTIC_FLAGS: "-Werror -Werror=deprecated-declarations -Wno-error=cpp -Werror=unused-variable -Werror=unused-but-set-variable -Werror=unused-function"
    EXTRA_CFLAGS: "${PEDANTIC_FLAGS}"
    EXTRA_CXXFLAGS: "${PEDANTIC_FLAGS}"
    EXAMPLE_TARGETS: "esp32"
    EXAMPLES: "switch led_light fan temperature_sensor multi_device gpio homekit_switch"
    COMPONENT_MGR_142: "0" # Fix component manager version to 1.4.2
  script:
    - *build_all_examples

build_idf_v5.0:
  extends: .build_template
  image: espressif/idf:release-v5.0
  variables:
    EXAMPLE_TARGETS: "esp32 esp32s2 esp32c3 esp32s3"
    COMPONENT_MGR_142: "1"

build_idf_v5.1:
  extends: .build_template
  image: espressif/idf:release-v5.1
  variables:
    EXAMPLE_TARGETS: "esp32 esp32s2 esp32c3 esp32s3 esp32c6 esp32c2 esp32h2"

build_idf_v5.2:
  extends: .build_template
  image: espressif/idf:release-v5.2
  variables:
    EXAMPLE_TARGETS: "esp32 esp32s2 esp32c3 esp32s3 esp32c6 esp32c2 esp32h2"

build_idf_v5.3:
  extends: .build_template
  image: espressif/idf:release-v5.3
  variables:
    EXAMPLE_TARGETS: "esp32 esp32s2 esp32c3 esp32s3 esp32c6 esp32c2 esp32h2"

build_idf_v5.4:
  extends: .build_template
  image: espressif/idf:release-v5.4
  variables:
    EXAMPLE_TARGETS: "esp32 esp32s2 esp32c3 esp32s3 esp32c6 esp32c2 esp32h2"

build_idf_v5.5:
  extends: .build_template
  image: espressif/idf:release-v5.5
  variables:
    EXAMPLE_TARGETS: "esp32 esp32s2 esp32c3 esp32s3 esp32c6 esp32c2 esp32h2"

build_thread_br:
  extends: .build_template
  image: espressif/idf:release-v5.3
  variables:
    PEDANTIC_FLAGS: "-Werror -Werror=deprecated-declarations -Wno-error=cpp -Werror=unused-variable -Werror=unused-function"
    EXAMPLE_TARGETS: "esp32s3"
    EXAMPLES: "thread_br"

build_matter:
  stage: build
  image: espressif/esp-matter:latest_idf_v5.4.1@sha256:988acc285766319b64e8ed69ca0a0ea80fab9de90640a597e7b2f9f0c453d80c
  tags:
    - build
  variables:
    PEDANTIC_FLAGS: ""
    EXAMPLE_TARGETS: "esp32 esp32c3 esp32s3 esp32c6 esp32c2"
    EXAMPLES: "matter/matter_light matter/matter_switch"
    CONTROLLER_EXAMPLE_TARGET: "esp32s3"
  script:
    - *build_matter_examples
    - *build_matter_controller_example

build_zigbee_gateway:
  extends: .build_template
  image: espressif/idf:release-v5.3
  variables:
    EXAMPLE_TARGETS: "esp32s3"
    EXAMPLES: "zigbee_gateway"

build_docs:
  stage: build
  image: $ESP_DOCS_ENV_IMAGE
  tags:
    - build_docs
  artifacts:
    paths:
      - docs/_build/*/*/*.txt
      - docs/_build/*/*/html/*
    expire_in: 4 days
  # No cleaning when the artifacts
  after_script: []
  script:
    - cd docs
    - pip install -r requirements.txt
    - build-docs -l en -t esp32

.deploy_docs_template:
  stage: deploy
  image: $ESP_DOCS_ENV_IMAGE
  tags:
    - deploy_docs
  needs:
    - build_docs
  only:
    changes:
      - "docs/**/*"
  script:
    - source ${CI_PROJECT_DIR}/docs/utils.sh
    - add_doc_server_ssh_keys $DOCS_DEPLOY_PRIVATEKEY $DOCS_DEPLOY_SERVER $DOCS_DEPLOY_SERVER_USER
    - export GIT_VER=$(git describe --always)
    - pip install -r ${CI_PROJECT_DIR}/docs/requirements.txt
    - deploy-docs

deploy_docs_preview:
  extends:
    - .deploy_docs_template
  except:
    refs:
      - master
  variables:
    TYPE: "preview"
    DOCS_BUILD_DIR: "${CI_PROJECT_DIR}/docs/_build/"
    DOCS_DEPLOY_PRIVATEKEY: "$DOCS_PREVIEW_PRIVATEKEY"
    DOCS_DEPLOY_SERVER: "$DOCS_PREVIEW_SERVER"
    DOCS_DEPLOY_SERVER_USER: "$DOCS_PREVIEW_USER"
    DOCS_DEPLOY_PATH: "$DOCS_PREVIEW_PATH"
    DOCS_DEPLOY_URL_BASE: "$DOCS_PREVIEW_URL_BASE"

deploy_docs_production:
  extends:
    - .deploy_docs_template
  only:
    refs:
      - master
  variables:
    TYPE: "production"
    DOCS_BUILD_DIR: "${CI_PROJECT_DIR}/docs/_build/"
    DOCS_DEPLOY_PRIVATEKEY: "$DOCS_PROD_PRIVATEKEY"
    DOCS_DEPLOY_SERVER: "$DOCS_PROD_SERVER"
    DOCS_DEPLOY_SERVER_USER: "$DOCS_PROD_USER"
    DOCS_DEPLOY_PATH: "$DOCS_PROD_PATH"
    DOCS_DEPLOY_URL_BASE: "$DOCS_PROD_URL_BASE"

push_master_to_github:
  stage: deploy
  image: espressif/idf:latest
  tags:
    - build
  when: on_success
  dependencies: []
  only:
    - master
  script:
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo -n $GH_PUSH_KEY > ~/.ssh/id_rsa_base64
    - base64 --decode --ignore-garbage ~/.ssh/id_rsa_base64 > ~/.ssh/id_rsa
    - chmod 600 ~/.ssh/id_rsa
    - echo -e "Host github.com\n\tStrictHostKeyChecking no\n" >> ~/.ssh/config
    - git remote remove github &>/dev/null || true
    - git remote <NAME_EMAIL>:espressif/esp-rainmaker.git
    - git push github "${CI_COMMIT_SHA}:refs/heads/${CI_COMMIT_REF_NAME}"

build_esp32c5_preview:
  extends: .build_template
  image: espressif/idf:release-v5.5
  variables:
    EXAMPLE_TARGETS: "esp32c5"
    EXAMPLES: "switch led_light fan temperature_sensor multi_device gpio"
  script:
    - pip install --upgrade idf-component-manager
    - for EXAMPLE in $EXAMPLES; do
    - cd $CI_PROJECT_DIR/examples/$EXAMPLE
    - echo Building $EXAMPLE
    - for TARGET in $EXAMPLE_TARGETS; do
    - echo Building for $TARGET
    - idf.py --preview set-target $TARGET
    - idf.py build
    - mkdir -p $CI_PROJECT_DIR/esp-rainmaker-bins-${CI_JOB_ID}/$EXAMPLE/$TARGET/
    - cp $CI_PROJECT_DIR/examples/$EXAMPLE/build/*.bin $CI_PROJECT_DIR/esp-rainmaker-bins-${CI_JOB_ID}/$EXAMPLE/$TARGET/
    - done
    - echo Build Complete for $EXAMPLE
    - done
    # Generating zip file for binaries generated
    - cd $CI_PROJECT_DIR
    - echo Generating zip file for binaries generated
    - tar -zcvf esp-rainmaker-bins-${CI_JOB_ID}.zip esp-rainmaker-bins-${CI_JOB_ID}/
