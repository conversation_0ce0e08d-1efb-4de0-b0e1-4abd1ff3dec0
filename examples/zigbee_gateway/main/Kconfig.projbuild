menu "ESP Rainmaker Zigbee Gateway Example"
    config ZIGBEE_INSTALLCODE_ENABLED
        bool 'Enable zigbee install code'
        default False
        help
            Select this option to enable the Zigbee Install Code feature, it allows only Zigbee devices with the
            install code mode enabled to join the network formed by the gateway.

    choice ZIGBEE_GATEWAY_BOARD_TYPE
        prompt "Zigbee gateway board type"
        default ESP_GATEWAY_BOARD_DEV_KIT
        help
            The board running the Zigbee gateway.

        config ESP_GATEWAY_BOARD_DEV_KIT
            bool "ESP Zigbee gateway board dev kit"
            help
                Integrated gateway dev kit

        config M5STACK_ZIGBEE_GATEWAY_BOARD
            bool "M5Stack Zigbee gateway board"
            help
                M5Stack CoreS3 with Module Gateway H2
    endchoice

    menu "Board Configuration"
        config PIN_TO_RCP_RESET
            int "Pin to RCP reset"
            default "7"

        config PIN_TO_RCP_BOOT
            int "Pin to RCP boot"
            default "18" if M5STACK_ZIGBEE_GATEWAY_BOARD
            default "8"

        config PIN_TO_RCP_TX
            int "Pin to RCP TX"
            default "10" if M5STACK_ZIGBEE_GATEWAY_BOARD
            default "17"

        config PIN_TO_RCP_RX
            int "Pin to RCP RX"
            default "17" if M5STACK_ZIGBEE_GATEWAY_BOARD
            default "18"
    endmenu

endmenu
