# Name,   Type, SubType, Offset,  Size, Flags
# Note: Firmware partition offset needs to be 64K aligned, initial 36K (9 sectors) are reserved for bootloader and partition table
sec_cert,  0x3F, ,0xd000,    0x3000, ,  # Never mark this as an encrypted partition
nvs,      data, nvs,     0x10000,   0x6000,
otadata,  data, ota,     ,          0x2000
phy_init, data, phy,     ,          0x1000,
fctry,    data, nvs,     ,          0x6000,
factory,  app,  factory, ,          2M,
zb_storage, data, fat,   ,          16K,
zb_fct,     data, fat,   ,          1K,
rcp_fw,     data, spiffs,,          640k,
