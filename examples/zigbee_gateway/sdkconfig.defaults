CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
CONFIG_PARTITION_TABLE_SINGLE_APP=
CONFIG_PARTITION_TABLE_TWO_OTA=
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_OFFSET=0x8000
CONFIG_PARTITION_TABLE_MD5=y

# mbedtls
CONFIG_MBEDTLS_DYNAMIC_BUFFER=y
CONFIG_MBEDTLS_DYNAMIC_FREE_PEER_CERT=y
CONFIG_MBEDTLS_DYNAMIC_FREE_CONFIG_DATA=y
CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN=y

# For BLE Provisioning using NimBLE stack (Not applicable for ESP32-S2)
CONFIG_BT_ENABLED=y
CONFIG_BTDM_CTRL_MODE_BLE_ONLY=y
CONFIG_BT_NIMBLE_ENABLED=y

# Temporary Fix for Timer Overflows
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=3120

# For additional security on reset to factory
CONFIG_ESP_RMAKER_USER_ID_CHECK=y

# Secure Local Control
CONFIG_ESP_RMAKER_LOCAL_CTRL_ENABLE=y
CONFIG_ESP_RMAKER_LOCAL_CTRL_SECURITY_1=y

# Application Rollback
CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE=y

CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0=n
CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU1=n

#compiler options
CONFIG_COMPILER_OPTIMIZATION_SIZE=y

#event stack
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=3400

CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=6240
CONFIG_ESP_RMAKER_WORK_QUEUE_TASK_STACK=8192

#
# Zboss
#
CONFIG_ZB_ENABLED=y
CONFIG_ZB_ZCZR=y
# end of Zboss

#
# Serial flasher config
#
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
# end of Serial flasher config

#
# mbedTLS
#
CONFIG_MBEDTLS_HARDWARE_AES=n
CONFIG_MBEDTLS_HARDWARE_MPI=n
CONFIG_MBEDTLS_HARDWARE_SHA=n
CONFIG_MBEDTLS_CMAC_C=y
CONFIG_MBEDTLS_SSL_PROTO_DTLS=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECJPAKE=y
# end of TLS Key Exchange Methods

CONFIG_MBEDTLS_ECJPAKE_C=y
# end of mbedTLS

CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=4096

#
# Zboss
#
CONFIG_ZB_ENABLED=y
CONFIG_ZB_ZCZR=y
# end of Zboss
# end of Component config

#
# IEEE802154
#
CONFIG_IEEE802154_RECEIVE_DONE_HANDLER=y
# end of IEEE802154

# If ESP-Insights is enabled, we need MQTT transport selected
# Takes out manual efforts to enable this option
CONFIG_ESP_INSIGHTS_TRANSPORT_MQTT=y
