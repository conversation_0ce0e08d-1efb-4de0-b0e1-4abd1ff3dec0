menu "Example Configuration"

    config EXAMPLE_USE_HARDCODED_SETUP_CODE
        bool "Use hard-coded setup code"
        default y
        help
            HomeKit does not recommend having the setup code programmed in the accessory as is.
            Instead, it expects setup info (salt-verifier pair) generated for the given setup code.
            Use this option only for testing purposes. For production, the setup info should be
            used.

    config EXAMPLE_SETUP_CODE
        string "HomeKit Setup Code"
        default "111-22-333"
        depends on EXAMPLE_USE_HARDCODED_SETUP_CODE
        help
            Setup code to be used for HomeKot pairing, if hard-coded setup code is enabled.

    config EXAMPLE_SETUP_ID
        string "HomeKit Setup Id"
        default "ES32"
        depends on EXAMPLE_USE_HARDCODED_SETUP_CODE
        help
            Setup id to be used for HomeKot pairing, if hard-coded setup code is enabled.

    config EXAMPLE_BOARD_BUTTON_GPIO

        int "Boot Button GPIO"
        default 9 if IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32C6 || IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32H2
        default 0
        help
            <PERSON>IO number on which the "Boot" button is connected. This is generally used
            by the application for custom operations like toggling states, resetting to defaults, etc.

    config EXAMPLE_OUTPUT_GPIO
        int "Output GPIO"
        default 19
        help
            This is an output GPIO that will be connected to a relay or other driver circuit in most cases.
            If the power changes, this GPIO output level will also change.

endmenu
