CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y

#
# Use partition table which makes use of flash to the fullest
# Can be used for other platforms as well. But please keep in mind that fctry partition address is
# different than default, and the new address needs to be specified to `rainmaker.py claim`
#
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions_4mb_optimised.csv"

# To accomodate security features
CONFIG_PARTITION_TABLE_OFFSET=0xc000

# mbedtls
CONFIG_MBEDTLS_DYNAMIC_BUFFER=y
CONFIG_MBEDTLS_DYNAMIC_FREE_PEER_CERT=y
CONFIG_MBEDTLS_DYNAMIC_FREE_CONFIG_DATA=y
CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN=y

# For BLE Provisioning using NimBLE stack (Not applicable for ESP32-S2)
CONFIG_BT_ENABLED=y
CONFIG_BTDM_CTRL_MODE_BLE_ONLY=y
CONFIG_BT_NIMBLE_ENABLED=y

# Temporary Fix for Timer Overflows
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=3120

# For additional security on reset to factory
CONFIG_ESP_RMAKER_USER_ID_CHECK=y

# Secure Local Control
CONFIG_ESP_RMAKER_LOCAL_CTRL_AUTO_ENABLE=y
#CONFIG_ESP_RMAKER_LOCAL_CTRL_ENABLE is deprecated but will continue to work
CONFIG_ESP_RMAKER_LOCAL_CTRL_SECURITY_1=y

# Application Rollback
CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE=y

# If ESP-Insights is enabled, we need MQTT transport selected
# Takes out manual efforts to enable this option
CONFIG_ESP_INSIGHTS_TRANSPORT_MQTT=y
