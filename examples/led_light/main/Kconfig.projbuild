menu "Example Configuration"

    config EXAMPLE_BOARD_BUTTON_GPIO
        int "Boot Button GPIO"
        default 28 if IDF_TARGET_ESP32C5
        default 9 if IDF_TARGET_ESP32C3 || IDF_TARGET_ESP32C6 || IDF_TARGET_ESP32C2 || IDF_TARGET_ESP32H2
        default 0
        help
            GPIO number on which the "Boot" button is connected. This is generally used
            by the application for custom operations like toggling states, resetting to defaults, etc.

endmenu
