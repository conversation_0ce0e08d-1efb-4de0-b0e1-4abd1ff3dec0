# sdkconfig replacement configurations for deprecated options formatted as
# CONFIG_DEPRECATED_OPTION CONFIG_NEW_OPTION


CONFIG_APP_WIFI_PROV_SHOW_QR            CONFIG_APP_NETWORK_PROV_SHOW_QR
CONFIG_APP_WIFI_PROV_MAX_POP_MISMATCH   CONFIG_APP_NETWORK_PROV_MAX_POP_MISMATCH
CONFIG_APP_WIFI_PROV_TRANSPORT_SOFTAP   CONFIG_APP_NETWORK_PROV_TRANSPORT_SOFTAP
CONFIG_APP_WIFI_PROV_TRANSPORT_BLE      CONFIG_APP_NETWORK_PROV_TRANSPORT_BLE
CONFIG_APP_WIFI_PROV_TRANSPORT          CONFIG_APP_NETWORK_PROV_TRANSPORT
CONFIG_APP_WIFI_RESET_PROV_ON_FAILURE   CONFIG_APP_NETWORK_RESET_PROV_ON_FAILURE
CONFIG_APP_WIFI_SHOW_DEMO_INTRO_TEXT    CONFIG_APP_NETWORK_SHOW_DEMO_INTRO_TEXT
CONFIG_APP_WIFI_PROV_TIMEOUT_PERIOD     CONFIG_APP_NETWORK_PROV_TIMEOUT_PERIOD
CONFIG_APP_WIFI_PROV_NAME_PREFIX        CONFIG_APP_NETWORK_PROV_NAME_PREFIX
