CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_OFFSET=0x8000
CONFIG_PARTITION_TABLE_MD5=y

# mbedtls
CONFIG_MBEDTLS_DYNAMIC_BUFFER=y
CONFIG_MBEDTLS_DYNAMIC_FREE_PEER_CERT=y
CONFIG_MBEDTLS_DYNAMIC_FREE_CONFIG_DATA=y
CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN=y

# For BLE Provisioning using NimBLE stack (Not applicable for ESP32-S2)
CONFIG_BT_ENABLED=y
CONFIG_BTDM_CTRL_MODE_BLE_ONLY=y
CONFIG_BT_NIMBLE_ENABLED=y

# Temporary Fix for Timer Overflows
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=3120

# For additional security on reset to factory
CONFIG_ESP_RMAKER_USER_ID_CHECK=y

# Secure Local Control
CONFIG_ESP_RMAKER_LOCAL_CTRL_AUTO_ENABLE=y
#CONFIG_ESP_RMAKER_LOCAL_CTRL_ENABLE is deprecated but will continue to work
CONFIG_ESP_RMAKER_LOCAL_CTRL_SECURITY_1=y
CONFIG_ESP_RMAKER_LOCAL_CTRL_STACK_SIZE=8096

# Application Rollback
CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE=y

# Increase the MQTT task stack size to use generate new Thread dataset command
CONFIG_MQTT_USE_CUSTOM_CONFIG=y
CONFIG_MQTT_TASK_STACK_SIZE=7168

# mbedTLS
CONFIG_MBEDTLS_CMAC_C=y
CONFIG_MBEDTLS_SSL_PROTO_DTLS=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECJPAKE=y
CONFIG_MBEDTLS_ECJPAKE_C=y

# OpenThread
CONFIG_OPENTHREAD_ENABLED=y
CONFIG_OPENTHREAD_BORDER_ROUTER=y
CONFIG_OPENTHREAD_RADIO_SPINEL_UART=y
CONFIG_OPENTHREAD_LOG_LEVEL_DYNAMIC=n
CONFIG_OPENTHREAD_LOG_LEVEL_NOTE=y

# lwIP
CONFIG_LWIP_IPV6_FORWARD=y
CONFIG_LWIP_IPV6_NUM_ADDRESSES=8
CONFIG_LWIP_MULTICAST_PING=y
CONFIG_LWIP_NETIF_STATUS_CALLBACK=y
CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT=y
CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT=y
CONFIG_LWIP_HOOK_IP6_INPUT_CUSTOM=y
CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_CUSTOM=y
CONFIG_LWIP_IPV6_AUTOCONFIG=y

# mDNS
CONFIG_MDNS_MULTIPLE_INSTANCE=y

# ESP System Settings
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=3584

# USB console for Thread border board
CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG=y

# Firmware size optimization
CONFIG_COMPILER_OPTIMIZATION_SIZE=y
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT=y
CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT=y
CONFIG_COMPILER_SAVE_RESTORE_LIBCALLS=y
CONFIG_NEWLIB_NANO_FORMAT=y
CONFIG_ESP_ERR_TO_NAME_LOOKUP=n

# ESP Thread border router board
CONFIG_ESP_THREAD_BR_BOARD_DEV_KIT=y