/*
 * ESP RainMaker Username/Password Authentication Example
 *
 * This example demonstrates how to use username and password authentication
 * with ESP RainMaker MQTT connection instead of or in addition to certificate-based
 * authentication.
 *
 * To enable username/password authentication:
 * 1. Enable CONFIG_ESP_RMAKER_MQTT_USE_USERNAME_PASSWORD in menuconfig
 * 2. Set CONFIG_ESP_RMAKER_MQTT_USERNAME and CONFIG_ESP_RMAKER_MQTT_PASSWORD
 * 3. Optionally, set username/password programmatically using the API
 */

#include <string.h>
#include <esp_log.h>
#include <esp_rmaker_core.h>
#include <esp_rmaker_mqtt_glue.h>

static const char *TAG = "username_password_auth";

void app_main(void)
{
    /* Initialize NVS */
    esp_err_t err = nvs_flash_init();
    if (err == ESP_ERR_NVS_NO_FREE_PAGES || err == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        err = nvs_flash_init();
    }
    ESP_ERROR_CHECK(err);

    /* Initialize Wi-Fi */
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    /* Example: Set MQTT username and password programmatically */
#ifdef CONFIG_ESP_RMAKER_MQTT_USE_USERNAME_PASSWORD
    ESP_LOGI(TAG, "Setting MQTT username and password");
    
    /* Set custom username and password */
    err = esp_rmaker_set_mqtt_username("my_device_username");
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set MQTT username: %s", esp_err_to_name(err));
    }
    
    err = esp_rmaker_set_mqtt_password("my_secure_password");
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set MQTT password: %s", esp_err_to_name(err));
    }
    
    /* Verify the credentials were set */
    char *username = esp_rmaker_get_mqtt_username();
    if (username) {
        ESP_LOGI(TAG, "MQTT Username: %s", username);
        free(username);
    }
    
    /* Note: We don't log the password for security reasons */
    char *password = esp_rmaker_get_mqtt_password();
    if (password) {
        ESP_LOGI(TAG, "MQTT Password: [SET]");
        free(password);
    }
#else
    ESP_LOGW(TAG, "Username/password authentication is not enabled");
    ESP_LOGW(TAG, "Enable CONFIG_ESP_RMAKER_MQTT_USE_USERNAME_PASSWORD in menuconfig");
#endif

    /* Initialize ESP RainMaker */
    esp_rmaker_config_t rainmaker_cfg = {
        .enable_time_sync = false,
    };
    
    esp_rmaker_node_t *node = esp_rmaker_node_init(&rainmaker_cfg, "My Device", "Switch");
    if (!node) {
        ESP_LOGE(TAG, "Could not initialise node");
        return;
    }

    /* Create a simple switch device */
    esp_rmaker_device_t *switch_device = esp_rmaker_device_create("Switch", ESP_RMAKER_DEVICE_SWITCH, NULL);
    esp_rmaker_device_add_cb(switch_device, NULL, NULL);
    
    esp_rmaker_param_t *power_param = esp_rmaker_param_create("Power", ESP_RMAKER_PARAM_POWER, 
                                                              esp_rmaker_bool(false), PROP_FLAG_READ | PROP_FLAG_WRITE);
    esp_rmaker_device_add_param(switch_device, power_param);
    esp_rmaker_node_add_device(node, switch_device);

    /* Start ESP RainMaker */
    ESP_LOGI(TAG, "Starting ESP RainMaker");
    err = esp_rmaker_start();
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Could not start ESP RainMaker: %s", esp_err_to_name(err));
        return;
    }

    ESP_LOGI(TAG, "ESP RainMaker started successfully");
    ESP_LOGI(TAG, "MQTT will use username/password authentication if enabled");
}
