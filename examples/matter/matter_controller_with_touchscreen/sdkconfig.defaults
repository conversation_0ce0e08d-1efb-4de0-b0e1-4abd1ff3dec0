# Default to 921600 baud when flashing and monitoring device
CONFIG_ESPTO<PERSON>PY_BAUD_921600B=y
CONFIG_ESPTOOLPY_BAUD=921600
CONFIG_ESPTOOLPY_COMPRESSED=y
CONFIG_ESPTOOLPY_MONITOR_BAUD_115200B=y
CONFIG_ESPTOOLPY_MONITOR_BAUD=115200
# CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
CONFIG_ESPTOOLPY_FLASHSIZE_16MB=y
CONFIG_ESPTOOLPY_FLASHMODE_QIO=y
# CONFIG_ESPTOOLPY_FLASHSIZE_DETECT is not set

# cpu
CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_240=y
CONFIG_ESP32S3_INSTRUCTION_CACHE_32KB=y
CONFIG_ESP32S3_DATA_CACHE_64KB=y
CONFIG_ESP32S3_DATA_CACHE_LINE_64B=y


#enable BT
CONFIG_BT_ENABLED=y
CONFIG_BT_NIMBLE_ENABLED=y
CONFIG_BT_NIMBLE_MEM_ALLOC_MODE_EXTERNAL=y

# enable lwip ipv6 autoconfig
CONFIG_LWIP_IPV6_AUTOCONFIG=y

# Use a custom partition table
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_OFFSET=0x8000
CONFIG_PARTITION_TABLE_MD5=y

# Enable chip shell
CONFIG_ENABLE_CHIP_SHELL=y

# Temporary Fix for Timer Overflows
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=4096

#enable lwIP route hooks
CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT=y
CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT=y

# Button
CONFIG_BUTTON_PERIOD_TIME_MS=20
CONFIG_BUTTON_LONG_PRESS_TIME_MS=5000
CONFIG_ADC_BUTTON_MAX_BUTTON_PER_CHANNEL=4

# ESP RainMaker
CONFIG_ESP_RMAKER_USER_ID_CHECK=y
CONFIG_ESP_RMAKER_NO_CLAIM=y
CONFIG_ESP_RMAKER_USE_ESP_SECURE_CERT_MGR=y
CONFIG_ESP_RMAKER_READ_NODE_ID_FROM_CERT_CN=y

# ESP Matter
CONFIG_CHIP_FACTORY_NAMESPACE_PARTITION_LABEL="fctry"
CONFIG_ENABLE_ESP32_FACTORY_DATA_PROVIDER=y
CONFIG_ENABLE_ESP32_DEVICE_INSTANCE_INFO_PROVIDER=y
CONFIG_ENABLE_ESP32_DEVICE_INFO_PROVIDER=y
CONFIG_SEC_CERT_DAC_PROVIDER=y
CONFIG_DEVICE_VENDOR_ID=0x131B
CONFIG_DEVICE_PRODUCT_ID=0x2
CONFIG_ESP_MATTER_MEM_ALLOC_MODE_EXTERNAL=y
CONFIG_CONTROLLER_CUSTOM_CLUSTER_ENABLE=y

CONFIG_ESP_SECURE_CERT_DS_PERIPHERAL=n

# Enable controller
CONFIG_ESP_MATTER_CONTROLLER_ENABLE=y
CONFIG_ESP_MATTER_COMMISSIONER_ENABLE=n
CONFIG_CONTROLLER_CUSTOM_CLUSTER_ENABLE=y

# Increase stack size
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=4096
CONFIG_ESP_MATTER_CONSOLE_TASK_STACK=5120
CONFIG_CHIP_TASK_STACK_SIZE=15360
CONFIG_ESP_MAIN_TASK_STACK_SIZE=10240

# Increase udp endpoints number
CONFIG_NUM_UDP_ENDPOINTS=16

# Increase LwIP IPv6 address number to 6 (MAX_FABRIC + 1)
CONFIG_LWIP_IPV6_NUM_ADDRESSES=6

# lv config
CONFIG_LV_COLOR_16_SWAP=y
CONFIG_LV_MEM_CUSTOM=y
CONFIG_LV_FONT_FMT_TXT_LARGE=y
CONFIG_LV_FONT_MONTSERRAT_24=y
CONFIG_LV_FONT_MONTSERRAT_32=y
CONFIG_LV_USE_QRCODE=y
CONFIG_LV_USE_FONT_PLACEHOLDER=n

# Disable ble only for commission
CONFIG_USE_BLE_ONLY_FOR_COMMISSIONING=n

# BSP
CONFIG_BSP_LCD_DRAW_BUF_HEIGHT=10

CONFIG_ESP_ALLOW_BSS_SEG_EXTERNAL_MEMORY=y

# SPIRAM
CONFIG_SPIRAM=y
CONFIG_SPIRAM_MODE_OCT=y
CONFIG_SPIRAM_SPEED_80M=y
CONFIG_SPIRAM_MALLOC_ALWAYSINTERNAL=512
CONFIG_SPIRAM_MALLOC_RESERVE_INTERNAL=8192
CONFIG_SPIRAM_ALLOW_BSS_SEG_EXTERNAL_MEMORY=y
CONFIG_SPIRAM_TRY_ALLOCATE_WIFI_LWIP=y

CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG=y

# freertos
CONFIG_FREERTOS_VTASKLIST_INCLUDE_COREID=y
CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=y
CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH=y
CONFIG_FREERTOS_HZ=1000
CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT=n

CONFIG_LWIP_TCPIP_RECVMBOX_SIZE=16

# mbedTLS
CONFIG_MBEDTLS_EXTERNAL_MEM_ALLOC=y
CONFIG_MBEDTLS_DYNAMIC_BUFFER=y
CONFIG_MBEDTLS_DYNAMIC_FREE_PEER_CERT=y
CONFIG_MBEDTLS_DYNAMIC_FREE_CONFIG_DATA=y
CONFIG_MBEDTLS_SSL_PROTO_TLS1=n
CONFIG_MBEDTLS_SSL_PROTO_TLS1_1=n
CONFIG_MBEDTLS_TLS_CLIENT_ONLY=y
CONFIG_MBEDTLS_HKDF_C=y

# Temporary fix for allocating RSA interrupt failed
CONFIG_MBEDTLS_AES_USE_INTERRUPT=n
CONFIG_MBEDTLS_MPI_USE_INTERRUPT=n

# IPC (Inter-Processor Call)
CONFIG_ESP_IPC_TASK_STACK_SIZE=2048

# Increase attribute buffer largest
CONFIG_ESP_MATTER_ATTRIBUTE_BUFFER_LARGEST=2050

CONFIG_IDF_TARGET="esp32s3"

# Select one of the boards
CONFIG_BSP_BOARD_ESP32_S3_BOX_3=y

# Enable custom commission data to use dynamic qrcode
CONFIG_CUSTOM_COMMISSIONABLE_DATA_PROVIDER=y

# If ESP-Insights is enabled, we need MQTT transport selected
# Takes out manual efforts to enable this option
CONFIG_ESP_INSIGHTS_TRANSPORT_MQTT=y
