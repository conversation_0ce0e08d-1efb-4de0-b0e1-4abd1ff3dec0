#include "lvgl.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif
#ifndef LV_ATTRIBUTE_IMG_ESP_LOGO_TINY
#define LV_ATTRIBUTE_IMG_ESP_LOGO_TINY
#endif
const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_IMG_ESP_LOGO_TINY uint8_t esp_logo_tiny_map[] = {
#if LV_COLOR_DEPTH == 1 || LV_COLOR_DEPTH == 8
  /*Pixel format: Alpha 8 bit, Red: 3 bit, Green: 3 bit, Blue: 2 bit*/
  0x00, 0x00, 0xff, 0x01, 0xff, 0x6b, 0xff, 0xc7, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xc6, 0xff, 0x6a, 0xff, 0x01, 0x00, 0x00, 
  0xff, 0x02, 0xff, 0xb2, 0xfb, 0xff, 0xed, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xfb, 0xff, 0xff, 0xb0, 0xff, 0x01, 
  0xff, 0x6b, 0xfb, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xf6, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xfb, 0xff, 0xff, 0x6a, 
  0xff, 0xc7, 0xed, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xed, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xff, 0xc5, 
  0xff, 0xf6, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xfb, 0xff, 0xe9, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xf2, 0xff, 0xe9, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xf6, 
  0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xfb, 0xff, 0xed, 0xff, 0xee, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xee, 0xff, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xf2, 0xff, 0xf6, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xee, 0xff, 0xf2, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xe9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xed, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xf6, 0xff, 0xf7, 0xff, 0xf6, 0xff, 0xf2, 0xff, 0xe9, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xe9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xfb, 0xff, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xe9, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xed, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xf6, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xed, 0xff, 0xf2, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xf6, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xf6, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xf6, 0xff, 0xf6, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xe9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xf7, 0xff, 0xe9, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xee, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xed, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xee, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xf6, 0xff, 0xf2, 0xff, 0xe9, 0xff, 0xee, 0xff, 0xfb, 0xff, 0xed, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xee, 0xff, 0xfb, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xf2, 0xff, 0xfb, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xfb, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xff, 
  0xff, 0xf6, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xf2, 0xff, 0xfb, 0xff, 0xee, 0xff, 0xe9, 0xff, 0xf6, 0xff, 0xfb, 0xff, 0xee, 0xff, 0xe9, 0xff, 0xf2, 0xff, 0xf6, 0xff, 0xe9, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xed, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xff, 0xf6, 
  0xff, 0xc6, 0xed, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xf6, 0xff, 0xfb, 0xff, 0xf6, 0xff, 0xee, 0xff, 0xed, 0xff, 0xed, 0xff, 0xf2, 0xff, 0xf7, 0xff, 0xfb, 0xff, 0xf6, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xff, 0xc5, 
  0xff, 0x6a, 0xfb, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xf2, 0xff, 0xf6, 0xff, 0xf6, 0xff, 0xf2, 0xff, 0xed, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xfb, 0xff, 0xff, 0x68, 
  0xff, 0x01, 0xff, 0xb1, 0xfb, 0xff, 0xed, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xe9, 0xff, 0xed, 0xff, 0xfb, 0xff, 0xff, 0xaf, 0xff, 0x01, 
  0x00, 0x00, 0xff, 0x01, 0xff, 0x6a, 0xff, 0xc5, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xc5, 0xff, 0x69, 0xff, 0x01, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0x00, 0x00, 0x00, 0xff, 0xff, 0x01, 0xff, 0xff, 0x6b, 0xff, 0xff, 0xc7, 0x7e, 0xff, 0xf6, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x7e, 0xff, 0xf6, 0xff, 0xff, 0xc6, 0xff, 0xff, 0x6a, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0x02, 0xff, 0xff, 0xb2, 0x59, 0xfe, 0xff, 0xca, 0xea, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xca, 0xea, 0xff, 0x79, 0xfe, 0xff, 0xff, 0xff, 0xb0, 0xff, 0xff, 0x01, 
  0xff, 0xff, 0x6b, 0x59, 0xfe, 0xff, 0xc6, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x68, 0xea, 0xff, 0xf3, 0xf4, 0xff, 0x91, 0xf4, 0xff, 0xce, 0xf3, 0xff, 0x27, 0xea, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xc6, 0xe9, 0xff, 0x79, 0xfe, 0xff, 0xff, 0xff, 0x6a, 
  0xff, 0xff, 0xc7, 0xca, 0xea, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xc6, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xc6, 0xe9, 0xff, 0x0f, 0xf4, 0xff, 0xce, 0xf3, 0xff, 0x48, 0xea, 0xff, 0x07, 0xea, 0xff, 0x91, 0xf4, 0xff, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xfd, 0xff, 0xa9, 0xea, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xea, 0xea, 0xff, 0xff, 0xff, 0xc5, 
  0x7d, 0xff, 0xf6, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x68, 0xea, 0xff, 0x9a, 0xfe, 0xff, 0x27, 0xea, 0xff, 0x59, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9a, 0xfe, 0xff, 0xef, 0xf3, 0xff, 0x07, 0xea, 0xff, 0xb2, 0xf4, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xef, 0xf3, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x7e, 0xff, 0xf6, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xc6, 0xe9, 0xff, 0x9a, 0xfe, 0xff, 0x0b, 0xeb, 0xff, 0x6d, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xff, 0x6c, 0xf3, 0xff, 0xca, 0xea, 0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xf3, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x0f, 0xf4, 0xff, 0x34, 0xf5, 0xff, 0xa5, 0xe9, 0xff, 0x07, 0xea, 0xff, 0x4c, 0xf3, 0xff, 0x0f, 0xf4, 0xff, 0x55, 0xfd, 0xff, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2, 0xf4, 0xff, 0x28, 0xea, 0xff, 0xdb, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xa9, 0xea, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xba, 0xfe, 0xff, 0x27, 0xea, 0xff, 0x69, 0xea, 0xff, 0x14, 0xf5, 0xff, 0x75, 0xfd, 0xff, 0x14, 0xf5, 0xff, 0xae, 0xf3, 0xff, 0xe6, 0xe9, 0xff, 0xae, 0xf3, 0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x55, 0xfd, 0xff, 0x28, 0xea, 0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xfd, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0x89, 0xea, 0xff, 0x38, 0xfe, 0xff, 0xca, 0xea, 0xff, 0x9e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7e, 0xff, 0xff, 0xb2, 0xf4, 0xff, 0xe6, 0xe9, 0xff, 0x54, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x92, 0xf4, 0xff, 0xca, 0xea, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x27, 0xea, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0xef, 0xf3, 0xff, 0x91, 0xf4, 0xff, 0x38, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdb, 0xfe, 0xff, 0x69, 0xea, 0xff, 0x71, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0b, 0xeb, 0xff, 0xd2, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xce, 0xf3, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0x92, 0xf4, 0xff, 0xce, 0xf3, 0xff, 0x7d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbe, 0xff, 0xff, 0x0b, 0xeb, 0xff, 0x0f, 0xf4, 0xff, 0x38, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3d, 0xff, 0xff, 0x69, 0xea, 0xff, 0x55, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfe, 0xff, 0x07, 0xea, 0xff, 0x5d, 0xff, 0xff, 0x92, 0xf4, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0xd2, 0xf4, 0xff, 0x8d, 0xf3, 0xff, 0xba, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x79, 0xfe, 0xff, 0xd2, 0xf4, 0xff, 0x48, 0xea, 0xff, 0x4c, 0xf3, 0xff, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xba, 0xfe, 0xff, 0xe6, 0xe9, 0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xce, 0xf3, 0xff, 0xd2, 0xf4, 0xff, 0xb2, 0xf4, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0x30, 0xf4, 0xff, 0x30, 0xf4, 0xff, 0xef, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xff, 0x30, 0xf4, 0xff, 0x69, 0xea, 0xff, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x71, 0xf4, 0xff, 0xce, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x59, 0xfe, 0xff, 0x07, 0xea, 0xff, 0x07, 0xea, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0x2c, 0xf3, 0xff, 0x55, 0xfd, 0xff, 0xa5, 0xe9, 0xff, 0x0f, 0xf4, 0xff, 0x3c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0xf4, 0xff, 0x6d, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5d, 0xff, 0xff, 0xe6, 0xe9, 0xff, 0x7e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x27, 0xea, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0xc6, 0xe9, 0xff, 0xba, 0xfe, 0xff, 0xa6, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x69, 0xea, 0xff, 0x75, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbe, 0xff, 0xff, 0x48, 0xea, 0xff, 0x79, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x2c, 0xf3, 0xff, 0x96, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8d, 0xf3, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x14, 0xf5, 0xff, 0xce, 0xf3, 0xff, 0xa5, 0xe9, 0xff, 0x4c, 0xf3, 0xff, 0x96, 0xfd, 0xff, 0xca, 0xea, 0xff, 0x96, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x91, 0xf4, 0xff, 0x71, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0xf4, 0xff, 0x50, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8d, 0xf3, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x07, 0xea, 0xff, 0xba, 0xfe, 0xff, 0xe7, 0xe9, 0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0x18, 0xfe, 0xff, 0x0b, 0xeb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xfd, 0xff, 0x2c, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x75, 0xfd, 0xff, 0xae, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xd7, 0xfd, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x8d, 0xf3, 0xff, 0xf7, 0xfd, 0xff, 0xf3, 0xf4, 0xff, 0xbe, 0xff, 0xff, 0x0f, 0xf4, 0xff, 0x89, 0xea, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x38, 0xfe, 0xff, 0x0b, 0xeb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb6, 0xfd, 0xff, 0x07, 0xea, 0xff, 0xeb, 0xea, 0xff, 0xe6, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x1c, 0xff, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x71, 0xf4, 0xff, 0xd7, 0xfd, 0xff, 0xe6, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x50, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x55, 0xfd, 0xff, 0xae, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x34, 0xf5, 0xff, 0xa5, 0xe9, 0xff, 0x48, 0xea, 0xff, 0x38, 0xfe, 0xff, 0x07, 0xea, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x1c, 0xff, 0xff, 
  0x7e, 0xff, 0xf6, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xae, 0xf3, 0xff, 0x79, 0xfe, 0xff, 0x6d, 0xf3, 0xff, 0x07, 0xea, 0xff, 0x13, 0xf5, 0xff, 0x79, 0xfe, 0xff, 0x4c, 0xf3, 0xff, 0x48, 0xea, 0xff, 0x71, 0xf4, 0xff, 0x92, 0xf4, 0xff, 0x48, 0xea, 0xff, 0x0f, 0xf4, 0xff, 0xba, 0xfe, 0xff, 0x0b, 0xeb, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x7e, 0xff, 0xf6, 
  0xff, 0xff, 0xc6, 0xca, 0xea, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0x07, 0xea, 0xff, 0x14, 0xf5, 0xff, 0x59, 0xfe, 0xff, 0xd3, 0xf4, 0xff, 0x8d, 0xf3, 0xff, 0xca, 0xea, 0xff, 0x0b, 0xeb, 0xff, 0xae, 0xf3, 0xff, 0x34, 0xf5, 0xff, 0x9a, 0xfe, 0xff, 0x91, 0xf4, 0xff, 0xe6, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xeb, 0xea, 0xff, 0xff, 0xff, 0xc5, 
  0xff, 0xff, 0x6a, 0x79, 0xfe, 0xff, 0xc6, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xc6, 0xe9, 0xff, 0x2c, 0xf3, 0xff, 0x30, 0xf4, 0xff, 0xd2, 0xf4, 0xff, 0xb2, 0xf4, 0xff, 0x30, 0xf4, 0xff, 0xca, 0xea, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xc6, 0xe9, 0xff, 0x79, 0xfe, 0xff, 0xff, 0xff, 0x68, 
  0xff, 0xff, 0x01, 0xff, 0xff, 0xb1, 0x79, 0xfe, 0xff, 0xea, 0xea, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xa5, 0xe9, 0xff, 0xeb, 0xea, 0xff, 0x79, 0xfe, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0x01, 
  0x00, 0x00, 0x00, 0xff, 0xff, 0x01, 0xff, 0xff, 0x6a, 0xff, 0xff, 0xc5, 0x7e, 0xff, 0xf6, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x7e, 0xff, 0xf6, 0xff, 0xff, 0xc5, 0xff, 0xff, 0x69, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0x00, 0x00, 0x00, 0xff, 0xff, 0x01, 0xff, 0xff, 0x6b, 0xff, 0xff, 0xc7, 0xff, 0x7e, 0xf6, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x7e, 0xf6, 0xff, 0xff, 0xc6, 0xff, 0xff, 0x6a, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0x02, 0xff, 0xff, 0xb2, 0xfe, 0x59, 0xff, 0xea, 0xca, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0xca, 0xff, 0xfe, 0x79, 0xff, 0xff, 0xff, 0xb0, 0xff, 0xff, 0x01, 
  0xff, 0xff, 0x6b, 0xfe, 0x59, 0xff, 0xe9, 0xc6, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0x68, 0xff, 0xf4, 0xf3, 0xff, 0xf4, 0x91, 0xff, 0xf3, 0xce, 0xff, 0xea, 0x27, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xc6, 0xff, 0xfe, 0x79, 0xff, 0xff, 0xff, 0x6a, 
  0xff, 0xff, 0xc7, 0xea, 0xca, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xc6, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xc6, 0xff, 0xf4, 0x0f, 0xff, 0xf3, 0xce, 0xff, 0xea, 0x48, 0xff, 0xea, 0x07, 0xff, 0xf4, 0x91, 0xff, 0xff, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xf7, 0xff, 0xea, 0xa9, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0xea, 0xff, 0xff, 0xff, 0xc5, 
  0xff, 0x7d, 0xf6, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0x68, 0xff, 0xfe, 0x9a, 0xff, 0xea, 0x27, 0xff, 0xfe, 0x59, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x9a, 0xff, 0xf3, 0xef, 0xff, 0xea, 0x07, 0xff, 0xf4, 0xb2, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xf3, 0xef, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x7e, 0xf6, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xc6, 0xff, 0xfe, 0x9a, 0xff, 0xeb, 0x0b, 0xff, 0xf3, 0x6d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1c, 0xff, 0xf3, 0x6c, 0xff, 0xea, 0xca, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xef, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xf4, 0x0f, 0xff, 0xf5, 0x34, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0x07, 0xff, 0xf3, 0x4c, 0xff, 0xf4, 0x0f, 0xff, 0xfd, 0x55, 0xff, 0xff, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xb2, 0xff, 0xea, 0x28, 0xff, 0xfe, 0xdb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xea, 0xa9, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xfe, 0xba, 0xff, 0xea, 0x27, 0xff, 0xea, 0x69, 0xff, 0xf5, 0x14, 0xff, 0xfd, 0x75, 0xff, 0xf5, 0x14, 0xff, 0xf3, 0xae, 0xff, 0xe9, 0xe6, 0xff, 0xf3, 0xae, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x55, 0xff, 0xea, 0x28, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xf7, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0x89, 0xff, 0xfe, 0x38, 0xff, 0xea, 0xca, 0xff, 0xff, 0x9e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7e, 0xff, 0xf4, 0xb2, 0xff, 0xe9, 0xe6, 0xff, 0xfd, 0x54, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x92, 0xff, 0xea, 0xca, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xea, 0x27, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xf3, 0xef, 0xff, 0xf4, 0x91, 0xff, 0xfe, 0x38, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xdb, 0xff, 0xea, 0x69, 0xff, 0xf4, 0x71, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xeb, 0x0b, 0xff, 0xf4, 0xd2, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xce, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xf4, 0x92, 0xff, 0xf3, 0xce, 0xff, 0xff, 0x7d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbe, 0xff, 0xeb, 0x0b, 0xff, 0xf4, 0x0f, 0xff, 0xfe, 0x38, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x3d, 0xff, 0xea, 0x69, 0xff, 0xfd, 0x55, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfb, 0xff, 0xea, 0x07, 0xff, 0xff, 0x5d, 0xff, 0xf4, 0x92, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xf4, 0xd2, 0xff, 0xf3, 0x8d, 0xff, 0xfe, 0xba, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x79, 0xff, 0xf4, 0xd2, 0xff, 0xea, 0x48, 0xff, 0xf3, 0x4c, 0xff, 0xff, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xba, 0xff, 0xe9, 0xe6, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xce, 0xff, 0xf4, 0xd2, 0xff, 0xf4, 0xb2, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xf4, 0x30, 0xff, 0xf4, 0x30, 0xff, 0xf3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xf4, 0x30, 0xff, 0xea, 0x69, 0xff, 0xff, 0x5d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x71, 0xff, 0xf3, 0xce, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x59, 0xff, 0xea, 0x07, 0xff, 0xea, 0x07, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xf3, 0x2c, 0xff, 0xfd, 0x55, 0xff, 0xe9, 0xa5, 0xff, 0xf4, 0x0f, 0xff, 0xff, 0x3c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x30, 0xff, 0xf3, 0x6d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5d, 0xff, 0xe9, 0xe6, 0xff, 0xff, 0x7e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xea, 0x27, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xc6, 0xff, 0xfe, 0xba, 0xff, 0xe9, 0xa6, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0x69, 0xff, 0xfd, 0x75, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbe, 0xff, 0xea, 0x48, 0xff, 0xfe, 0x79, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x2c, 0xff, 0xfd, 0x96, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x8d, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xf5, 0x14, 0xff, 0xf3, 0xce, 0xff, 0xe9, 0xa5, 0xff, 0xf3, 0x4c, 0xff, 0xfd, 0x96, 0xff, 0xea, 0xca, 0xff, 0xfd, 0x96, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x91, 0xff, 0xf4, 0x71, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xd2, 0xff, 0xf4, 0x50, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x8d, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0x07, 0xff, 0xfe, 0xba, 0xff, 0xe9, 0xe7, 0xff, 0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x18, 0xff, 0xeb, 0x0b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xf7, 0xff, 0xf3, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x75, 0xff, 0xf3, 0xae, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xd7, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xf3, 0x8d, 0xff, 0xfd, 0xf7, 0xff, 0xf4, 0xf3, 0xff, 0xff, 0xbe, 0xff, 0xf4, 0x0f, 0xff, 0xea, 0x89, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x38, 0xff, 0xeb, 0x0b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xb6, 0xff, 0xea, 0x07, 0xff, 0xea, 0xeb, 0xff, 0xe9, 0xe6, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x1c, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xf4, 0x71, 0xff, 0xfd, 0xd7, 0xff, 0xe9, 0xe6, 0xff, 0xe9, 0xa5, 0xff, 0xf4, 0x50, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x55, 0xff, 0xf3, 0xae, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x34, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0x48, 0xff, 0xfe, 0x38, 0xff, 0xea, 0x07, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x1c, 0xff, 
  0xff, 0x7e, 0xf6, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xf3, 0xae, 0xff, 0xfe, 0x79, 0xff, 0xf3, 0x6d, 0xff, 0xea, 0x07, 0xff, 0xf5, 0x13, 0xff, 0xfe, 0x79, 0xff, 0xf3, 0x4c, 0xff, 0xea, 0x48, 0xff, 0xf4, 0x71, 0xff, 0xf4, 0x92, 0xff, 0xea, 0x48, 0xff, 0xf4, 0x0f, 0xff, 0xfe, 0xba, 0xff, 0xeb, 0x0b, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xff, 0x7e, 0xf6, 
  0xff, 0xff, 0xc6, 0xea, 0xca, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0x07, 0xff, 0xf5, 0x14, 0xff, 0xfe, 0x59, 0xff, 0xf4, 0xd3, 0xff, 0xf3, 0x8d, 0xff, 0xea, 0xca, 0xff, 0xeb, 0x0b, 0xff, 0xf3, 0xae, 0xff, 0xf5, 0x34, 0xff, 0xfe, 0x9a, 0xff, 0xf4, 0x91, 0xff, 0xe9, 0xe6, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0xeb, 0xff, 0xff, 0xff, 0xc5, 
  0xff, 0xff, 0x6a, 0xfe, 0x79, 0xff, 0xe9, 0xc6, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xc6, 0xff, 0xf3, 0x2c, 0xff, 0xf4, 0x30, 0xff, 0xf4, 0xd2, 0xff, 0xf4, 0xb2, 0xff, 0xf4, 0x30, 0xff, 0xea, 0xca, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xc6, 0xff, 0xfe, 0x79, 0xff, 0xff, 0xff, 0x68, 
  0xff, 0xff, 0x01, 0xff, 0xff, 0xb1, 0xfe, 0x79, 0xff, 0xea, 0xea, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xe9, 0xa5, 0xff, 0xea, 0xeb, 0xff, 0xfe, 0x79, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0x01, 
  0x00, 0x00, 0x00, 0xff, 0xff, 0x01, 0xff, 0xff, 0x6a, 0xff, 0xff, 0xc5, 0xff, 0x7e, 0xf6, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x3c, 0xff, 0xff, 0x7e, 0xf6, 0xff, 0xff, 0xc5, 0xff, 0xff, 0x69, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 32
  0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x01, 0xff, 0xff, 0xff, 0x6b, 0xff, 0xff, 0xff, 0xc7, 0xed, 0xed, 0xfd, 0xf6, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xed, 0xed, 0xfd, 0xf6, 0xff, 0xff, 0xff, 0xc6, 0xff, 0xff, 0xff, 0x6a, 0xff, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 
  0xff, 0xff, 0xff, 0x02, 0xff, 0xff, 0xff, 0xb2, 0xc8, 0xca, 0xf9, 0xff, 0x52, 0x59, 0xeb, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x53, 0x5a, 0xeb, 0xff, 0xc9, 0xcc, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xff, 0xff, 0xff, 0x01, 
  0xff, 0xff, 0xff, 0x6b, 0xc8, 0xca, 0xf9, 0xff, 0x2f, 0x38, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x43, 0x4b, 0xea, 0xff, 0x99, 0x9e, 0xf3, 0xff, 0x8a, 0x8f, 0xf2, 0xff, 0x72, 0x78, 0xef, 0xff, 0x3b, 0x43, 0xe9, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x30, 0x39, 0xe7, 0xff, 0xca, 0xcc, 0xf9, 0xff, 0xff, 0xff, 0xff, 0x6a, 
  0xff, 0xff, 0xff, 0xc7, 0x52, 0x59, 0xeb, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x30, 0x39, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2e, 0x37, 0xe7, 0xff, 0x79, 0x7f, 0xf0, 0xff, 0x72, 0x78, 0xef, 0xff, 0x42, 0x4a, 0xe9, 0xff, 0x38, 0x41, 0xe8, 0xff, 0x8c, 0x91, 0xf2, 0xff, 0xe5, 0xe6, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfd, 0xff, 0xff, 0xb8, 0xbb, 0xf7, 0xff, 0x4c, 0x54, 0xeb, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x54, 0x5b, 0xec, 0xff, 0xff, 0xff, 0xff, 0xc5, 
  0xec, 0xed, 0xfd, 0xf6, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x44, 0x4c, 0xea, 0xff, 0xcd, 0xcf, 0xf9, 0xff, 0x3b, 0x44, 0xe9, 0xff, 0xc7, 0xc9, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xce, 0xd0, 0xf9, 0xff, 0x78, 0x7e, 0xf0, 0xff, 0x36, 0x3f, 0xe8, 0xff, 0x8f, 0x94, 0xf2, 0xff, 0xf8, 0xf9, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xf8, 0xfe, 0xff, 0x77, 0x7d, 0xf0, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xed, 0xee, 0xfd, 0xf6, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2e, 0x37, 0xe7, 0xff, 0xcd, 0xcf, 0xf9, 0xff, 0x5b, 0x62, 0xec, 0xff, 0x65, 0x6c, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xe1, 0xfb, 0xff, 0x64, 0x6b, 0xed, 0xff, 0x51, 0x59, 0xeb, 0xff, 0xe1, 0xe2, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0x77, 0x7d, 0xf0, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x79, 0x7f, 0xf0, 0xff, 0xa0, 0xa4, 0xf4, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x38, 0x40, 0xe8, 0xff, 0x63, 0x6a, 0xed, 0xff, 0x79, 0x7f, 0xf0, 0xff, 0xa6, 0xaa, 0xf5, 0xff, 0xe7, 0xe8, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0x8f, 0x94, 0xf2, 0xff, 0x3d, 0x46, 0xe9, 0xff, 0xd6, 0xd7, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xf9, 0xfe, 0xff, 0x4b, 0x53, 0xeb, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xd3, 0xd5, 0xfa, 0xff, 0x3c, 0x44, 0xe9, 0xff, 0x46, 0x4e, 0xea, 0xff, 0x9d, 0xa2, 0xf4, 0xff, 0xab, 0xae, 0xf5, 0xff, 0x9e, 0xa2, 0xf4, 0xff, 0x6d, 0x73, 0xee, 0xff, 0x34, 0x3c, 0xe8, 0xff, 0x6d, 0x74, 0xee, 0xff, 0xdd, 0xdf, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa6, 0xaa, 0xf5, 0xff, 0x3e, 0x46, 0xe9, 0xff, 0xe1, 0xe2, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb9, 0xbc, 0xf7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x48, 0x50, 0xea, 0xff, 0xc0, 0xc3, 0xf8, 0xff, 0x50, 0x58, 0xeb, 0xff, 0xf0, 0xf1, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xed, 0xee, 0xfd, 0xff, 0x91, 0x96, 0xf2, 0xff, 0x34, 0x3d, 0xe8, 0xff, 0xa3, 0xa7, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8e, 0x92, 0xf2, 0xff, 0x50, 0x58, 0xeb, 0xff, 0xf8, 0xf9, 0xfe, 0xff, 0xfd, 0xfd, 0xff, 0xff, 0x3c, 0x44, 0xe9, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x78, 0x7e, 0xf0, 0xff, 0x8a, 0x8f, 0xf2, 0xff, 0xc1, 0xc4, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd7, 0xd9, 0xfa, 0xff, 0x46, 0x4e, 0xea, 0xff, 0x88, 0x8d, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfd, 0xff, 0xff, 0x5a, 0x61, 0xec, 0xff, 0x94, 0x99, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0x72, 0x78, 0xef, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x8e, 0x92, 0xf2, 0xff, 0x72, 0x78, 0xef, 0xff, 0xea, 0xeb, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf4, 0xfe, 0xff, 0x59, 0x60, 0xec, 0xff, 0x7a, 0x7f, 0xf0, 0xff, 0xc3, 0xc6, 0xf8, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0xe6, 0xfc, 0xff, 0x46, 0x4e, 0xea, 0xff, 0xa6, 0xaa, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xda, 0xdb, 0xfb, 0xff, 0x37, 0x40, 0xe8, 0xff, 0xe9, 0xea, 0xfd, 0xff, 0x8d, 0x92, 0xf2, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x93, 0x98, 0xf3, 0xff, 0x6c, 0x72, 0xee, 0xff, 0xd2, 0xd4, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcb, 0xcd, 0xf9, 0xff, 0x93, 0x97, 0xf3, 0xff, 0x41, 0x49, 0xe9, 0xff, 0x60, 0x67, 0xed, 0xff, 0xe7, 0xe8, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0xd3, 0xfa, 0xff, 0x34, 0x3d, 0xe7, 0xff, 0xe1, 0xe2, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x72, 0x78, 0xef, 0xff, 0x93, 0x97, 0xf3, 0xff, 0x8e, 0x93, 0xf2, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x7f, 0x84, 0xf0, 0xff, 0x80, 0x85, 0xf1, 0xff, 0x75, 0x7b, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xf5, 0xfe, 0xff, 0x7f, 0x84, 0xf0, 0xff, 0x46, 0x4e, 0xea, 0xff, 0xe8, 0xe9, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x88, 0x8d, 0xf1, 0xff, 0x72, 0x78, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc8, 0xca, 0xf9, 0xff, 0x38, 0x41, 0xe8, 0xff, 0x3a, 0x42, 0xe9, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x5d, 0x64, 0xed, 0xff, 0xa6, 0xa9, 0xf5, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x7b, 0x80, 0xf0, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0x7e, 0x83, 0xf0, 0xff, 0x65, 0x6c, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe8, 0xe9, 0xfc, 0xff, 0x32, 0x3b, 0xe7, 0xff, 0xed, 0xee, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfd, 0xff, 0xff, 0x3b, 0x43, 0xe9, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2e, 0x37, 0xe7, 0xff, 0xd4, 0xd6, 0xfa, 0xff, 0x2d, 0x36, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x46, 0x4e, 0xea, 0xff, 0xa8, 0xac, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf4, 0xfe, 0xff, 0x41, 0x49, 0xe9, 0xff, 0xcc, 0xce, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5e, 0x65, 0xed, 0xff, 0xad, 0xb1, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6a, 0x70, 0xee, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x9d, 0xa2, 0xf4, 0xff, 0x72, 0x78, 0xef, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x61, 0x68, 0xed, 0xff, 0xad, 0xb1, 0xf6, 0xff, 0x51, 0x59, 0xeb, 0xff, 0xaf, 0xb2, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x89, 0x8f, 0xf2, 0xff, 0x85, 0x8b, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x93, 0x97, 0xf3, 0xff, 0x82, 0x87, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6b, 0x71, 0xee, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x3a, 0x42, 0xe9, 0xff, 0xd1, 0xd3, 0xfa, 0xff, 0x35, 0x3e, 0xe8, 0xff, 0xe0, 0xe1, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbe, 0xc1, 0xf8, 0xff, 0x58, 0x5f, 0xec, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb9, 0xbc, 0xf7, 0xff, 0x5f, 0x66, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaa, 0xad, 0xf5, 0xff, 0x6d, 0x73, 0xee, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb5, 0xb8, 0xf7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x68, 0x6f, 0xee, 0xff, 0xb9, 0xbc, 0xf7, 0xff, 0x98, 0x9c, 0xf3, 0xff, 0xf4, 0xf5, 0xfe, 0xff, 0x7a, 0x7f, 0xf0, 0xff, 0x4a, 0x52, 0xea, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc3, 0xc5, 0xf8, 0xff, 0x5a, 0x61, 0xec, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2, 0xb5, 0xf6, 0xff, 0x36, 0x3f, 0xe8, 0xff, 0x56, 0x5d, 0xec, 0xff, 0x33, 0x3b, 0xe8, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xe0, 0xe2, 0xfc, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x87, 0x8c, 0xf1, 0xff, 0xb5, 0xb8, 0xf7, 0xff, 0x33, 0x3c, 0xe8, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x81, 0x87, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa6, 0xaa, 0xf5, 0xff, 0x70, 0x76, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xa3, 0xf4, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x41, 0x49, 0xe9, 0xff, 0xc1, 0xc4, 0xf8, 0xff, 0x39, 0x42, 0xe9, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xe0, 0xe2, 0xfc, 0xff, 
  0xed, 0xed, 0xfd, 0xf6, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x6d, 0x73, 0xee, 0xff, 0xcc, 0xce, 0xf9, 0xff, 0x67, 0x6d, 0xee, 0xff, 0x38, 0x40, 0xe8, 0xff, 0x9c, 0xa0, 0xf4, 0xff, 0xca, 0xcc, 0xf9, 0xff, 0x61, 0x68, 0xed, 0xff, 0x41, 0x49, 0xe9, 0xff, 0x85, 0x8b, 0xf1, 0xff, 0x8d, 0x92, 0xf2, 0xff, 0x41, 0x49, 0xe9, 0xff, 0x7c, 0x82, 0xf0, 0xff, 0xd1, 0xd3, 0xfa, 0xff, 0x58, 0x5f, 0xec, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0xed, 0xee, 0xfd, 0xf6, 
  0xff, 0xff, 0xff, 0xc6, 0x53, 0x5a, 0xeb, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x3a, 0x42, 0xe9, 0xff, 0x9d, 0xa2, 0xf4, 0xff, 0xc5, 0xc8, 0xf8, 0xff, 0x95, 0x9a, 0xf3, 0xff, 0x6a, 0x70, 0xee, 0xff, 0x53, 0x5a, 0xeb, 0xff, 0x59, 0x60, 0xec, 0xff, 0x6e, 0x74, 0xef, 0xff, 0xa1, 0xa5, 0xf4, 0xff, 0xcd, 0xcf, 0xf9, 0xff, 0x8c, 0x91, 0xf2, 0xff, 0x33, 0x3c, 0xe8, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x55, 0x5d, 0xec, 0xff, 0xff, 0xff, 0xff, 0xc5, 
  0xff, 0xff, 0xff, 0x6a, 0xc9, 0xcc, 0xf9, 0xff, 0x30, 0x39, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2e, 0x37, 0xe7, 0xff, 0x5d, 0x64, 0xed, 0xff, 0x7e, 0x83, 0xf0, 0xff, 0x94, 0x99, 0xf3, 0xff, 0x90, 0x95, 0xf2, 0xff, 0x7d, 0x83, 0xf0, 0xff, 0x53, 0x5a, 0xeb, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x30, 0x39, 0xe7, 0xff, 0xcc, 0xce, 0xf9, 0xff, 0xff, 0xff, 0xff, 0x68, 
  0xff, 0xff, 0xff, 0x01, 0xff, 0xff, 0xff, 0xb1, 0xca, 0xcc, 0xf9, 0xff, 0x54, 0x5b, 0xec, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x2c, 0x35, 0xe7, 0xff, 0x55, 0x5c, 0xec, 0xff, 0xcc, 0xce, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff, 0x01, 
  0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x01, 0xff, 0xff, 0xff, 0x6a, 0xff, 0xff, 0xff, 0xc5, 0xed, 0xee, 0xfd, 0xf6, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xe3, 0xe4, 0xfc, 0xff, 0xed, 0xee, 0xfd, 0xf6, 0xff, 0xff, 0xff, 0xc5, 0xff, 0xff, 0xff, 0x69, 0xff, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 
#endif
};

const lv_img_dsc_t esp_logo_tiny = {
  .header.always_zero = 0,
  .header.w = 24,
  .header.h = 24,
  .data_size = 576 * LV_IMG_PX_SIZE_ALPHA_BYTE,
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .data = esp_logo_tiny_map,
};
