if (EXISTS ${PROJECT_DIR}/sdkconfig)
    message(STATUS "matching board in sdkconfig")
    file(READ ${PROJECT_DIR}/sdkconfig SDKCONFIG_RULE)
    string(REGEX MATCH "CONFIG_BSP_BOARD_ESP32_S3_BOX_Lite=y" COMPILER_TARGET_IS_BOX_LITE "${SDKCONFIG_RULE}")
    string(REGEX MATCH "CONFIG_BSP_BOARD_ESP32_S3_BOX=y" COMPILER_TARGET_IS_ESP_BOX "${SDKCONFIG_RULE}")
    string(REGEX MATCH "CONFIG_BSP_BOARD_ESP32_S3_BOX_3=y" COMPILER_TARGET_IS_ESP_BOX_3 "${SDKCONFIG_RULE}")
    string(REGEX MATCH "CONFIG_BSP_BOARD_ESP32_S3_LCD_EV_BOARD=y" COMPILER_TARGET_IS_S3_LCD_EV_BOARD "${SDKCONFIG_RULE}")
    string(REGEX MATCH "CONFIG_BSP_BOARD_M5STACK_CORES3=y" COMPILER_TARGET_IS_M5STACK_CORES3 "${SDKCONFIG_RULE}")
elseif (EXISTS ${PROJECT_DIR}/sdkconfig.defaults)
    message(STATUS "matching board in sdkconfig.defaults")
    file(READ ${PROJECT_DIR}/sdkconfig.defaults SDKCONFIG_RULE)
    string(REGEX MATCH "CONFIG_BSP_BOARD_ESP32_S3_BOX_Lite=y" COMPILER_TARGET_IS_BOX_LITE "${SDKCONFIG_RULE}")
    string(REGEX MATCH "CONFIG_BSP_BOARD_ESP32_S3_BOX=y" COMPILER_TARGET_IS_ESP_BOX "${SDKCONFIG_RULE}")
    string(REGEX MATCH "CONFIG_BSP_BOARD_ESP32_S3_BOX_3=y" COMPILER_TARGET_IS_ESP_BOX_3 "${SDKCONFIG_RULE}")
    string(REGEX MATCH "CONFIG_BSP_BOARD_ESP32_S3_LCD_EV_BOARD=y" COMPILER_TARGET_IS_S3_LCD_EV_BOARD "${SDKCONFIG_RULE}")
    string(REGEX MATCH "CONFIG_BSP_BOARD_M5STACK_CORES3=y" COMPILER_TARGET_IS_M5STACK_CORES3 "${SDKCONFIG_RULE}")
endif()

if (COMPILER_TARGET_IS_ESP_BOX_3)
    message(STATUS "PLATFORM ESP32_S3_BOX_3.")
    set(priv_requires "esp-box-3")
elseif (COMPILER_TARGET_IS_BOX_LITE)
    message(STATUS "PLATFORM ESP32_S3_BOX_Lite.")
    set(priv_requires "esp-box-lite")
elseif (COMPILER_TARGET_IS_ESP_BOX)
    message(STATUS "PLATFORM ESP32_S3_BOX.")
    set(priv_requires "esp-box")
elseif (COMPILER_TARGET_IS_S3_LCD_EV_BOARD)
    message(STATUS "PLATFORM ESP32_S3_LCD_EV_Board.")
    set(priv_requires "esp32_s3_lcd_ev_board")
elseif (COMPILER_TARGET_IS_M5STACK_CORES3)
    message(STATUS "PLATFORM M5Stack CoreS3.")
    set(priv_requires "m5stack_core_s3")
else()
    message(STATUS "PLATFORM is not selected. Set ESP32-S3-BOX-3 as default.")
    set(priv_requires "esp-box-3")
endif()

idf_component_register(
    SRC_DIRS "."
    INCLUDE_DIRS "."
    PRIV_REQUIRES ${priv_requires})
