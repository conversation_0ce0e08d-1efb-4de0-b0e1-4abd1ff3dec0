# Enable Openthread Border Router, but don't run matter over Thread
CONFIG_OPENTHREAD_ENABLED=y
CONFIG_OPENTHREAD_LOG_LEVEL_DYNAMIC=n
CONFIG_OPENTHREAD_LOG_LEVEL_NOTE=y
CONFIG_OPENTHREAD_BORDER_ROUTER=y
CONFIG_OPENTHREAD_SRP_CLIENT=n
CONFIG_OPENTHREAD_DNS_CLIENT=n
CONFIG_THREAD_NETWORK_COMMISSIONING_DRIVER=n

# Enable auto update rcp
CONFIG_AUTO_UPDATE_RCP=y

#LwIP config for OpenThread
CONFIG_LWIP_IPV6_AUTOCONFIG=y
CONFIG_LWIP_IPV6_NUM_ADDRESSES=8
CONFIG_LWIP_MULTICAST_PING=y
CONFIG_LWIP_IPV6_FORWARD=y
CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT=y
CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT=y
CONFIG_LWIP_NETIF_STATUS_CALLBACK=y

# mbedTLS
CONFIG_MBEDTLS_HARDWARE_AES=y
CONFIG_MBEDTLS_HARDWARE_MPI=y
CONFIG_MBEDTLS_HARDWARE_SHA=y
CONFIG_MBEDTLS_CMAC_C=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECJPAKE=y
CONFIG_MBEDTLS_ECJPAKE_C=y
CONFIG_MBEDTLS_SSL_PROTO_DTLS=y

# MDNS platform
CONFIG_USE_MINIMAL_MDNS=n
CONFIG_ENABLE_EXTENDED_DISCOVERY=y
CONFIG_MDNS_MULTIPLE_INSTANCE=y

# Enable chip shell
CONFIG_ENABLE_CHIP_SHELL=y
CONFIG_ESP_MATTER_CONSOLE_TASK_STACK=4096

# Increase Stack size
CONFIG_CHIP_TASK_STACK_SIZE=10240
CONFIG_ESP_MAIN_TASK_STACK_SIZE=8192
CONFIG_THREAD_TASK_STACK_SIZE=8192

# Wi-Fi Settings
CONFIG_ENABLE_WIFI_STATION=y
CONFIG_ENABLE_WIFI_AP=n

# Enable Customer Cluster
CONFIG_CONTROLLER_CUSTOM_CLUSTER_ENABLE=y

# Disable chip test build
CONFIG_BUILD_CHIP_TESTS=n

# Disable OTA Requestor
CONFIG_ENABLE_OTA_REQUESTOR=n

# Use compact attribute storage mode
CONFIG_ESP_MATTER_NVS_USE_COMPACT_ATTR_STORAGE=y

# Disable route hook for matter since OTBR has already initialize the route hook
CONFIG_ENABLE_ROUTE_HOOK=n
