<?xml version="1.0"?>
<!--
Copyright (c) 2023 Project CHIP Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<configurator>
  <domain name="CHIP"/>
  <cluster>

    <name>MatterController</name>
    <domain>CHIP</domain>
    <code>0x131BFC01</code>
    <define>MATTER_CONTROLLER_CLUSTER</define>

    <description>Attributes and commands for matter_controller cluster.</description>

    <globalAttribute side="either" code="0xFFFD" value="2"/>

    <attribute side="server" code="0x0000" define="REFRESH_TOKEN" type="long_char_string" length="2048">RefreshToken</attribute>
    <attribute side="server" code="0x0001" define="REFRESH_TOKEN_VERIFIED" type="boolean" default="0">RefreshTokenVerified</attribute>
    <attribute side="server" code="0x0002" define="AUTHORIZED" type="boolean" default="0">Authorized</attribute>
    <attribute side="server" code="0x0003" define="USER_NOC_INSTALLED" type="boolean" default="0">UserNOCInstalled</attribute>
    <attribute side="server" code="0x0004" define="ENDPOINT_URL" type="char_string" length="64">EndpointUrl</attribute>
    <attribute side="server" code="0x0005" define="RMAKER_GROUP_ID" type="char_string" length="24">RmakerGroupId</attribute>
    <attribute side="server" code="0x0006" define="USER_NOC_FABRIC_INDEX" type="int8u" default="0">UserNOCFabricIndex</attribute>

    <command source="client" code="0x00" name="AppendRefreshToken" optional="false">
      <description>AppendRefreshToken command.</description>
      <arg name="AppendedRefreshToken" type="char_string"/>
    </command>

    <command source="client" code="0x01" name="ResetRefreshToken" optional="false">
      <description>ResetRefreshToken command.</description>
    </command>

    <command source="client" code="0x02" name="Authorize" optional="false">
      <description>Authorize command.</description>
      <arg name="EndpointUrl" type="char_string"/>
    </command>

    <command source="client" code="0x03" name="UpdateUserNOC" optional="false">
      <description>UpdateUserNOC command.</description>
    </command>

    <command source="client" code="0x04" name="UpdateDeviceList" optional="false">
      <description>UpdateDeviceList command.</description>
    </command>

  </cluster>
</configurator>
