# Matter + Rainmaker Controller Example

This example is expected to be compatible with the Rainmaker iOS app version 3.0.1 or newer.

## What to expect in this example?

- This demonstrates a Matter + RainMaker Controller. Matter is used for commissioning (also known as Wi-Fi provisioning) and local control, whereas RainMaker is used for remote control and OTA upgrades.
- To commission the device, scan the QR Code generated by the esp-matter's mfg_tool script using ESP RainMaker app.
- The commissioning flow for Matter Controller would be the same as other Matter devices. The controller will get node NOC (Node Operational Certificate) during Matter commissioning process.
- After commissioning, configure the controller with your user credentials. The controller will then install the user NOC and fetch the devices' information in the same fabric with the [custom controller cluster](https://github.com/espressif/esp-matter/tree/main/components/esp_matter_controller/controller_custom_cluster#matter-controller-cluster).
- After updating node NOC to user NOC, the Controller could control other devices in the same fabric locally using the [device console commands](https://docs.espressif.com/projects/esp-matter/en/latest/esp32/developing.html#controller-example).
- Single click the BOOT button will toggle the power of the selected remote device. This will also reflect on the phone app.
- Double click the BOOT button will change the selected remote device. The new selected device's node-id will be printed in the device console.

## OpenThread Border Router (OTBR) feature

### Hardware Platform

ESP Thread Border Router DevKit Board, see the [docs](https://github.com/espressif/esp-thread-br#hardware-platforms) for more information about the hardware platform.

### Build

The sdkconfig file `sdkconfig.defaults.otbr` is provided to enable the OTBR feature on the controller.
Build and flash the controller example with the sdkconfig file 'sdkconfig.defaults.otbr'

```
idf.py -D SDKCONFIG_DEFAULTS="sdkconfig.defaults;sdkconfig.defaults.otbr" set-target esp32s3 build
idf.py -p <PORT> erase-flash flash monitor
```

If you are using IDF v5.3.1 or later, there might be an error of `#error CONFIG_LWIP_IPV6_NUM_ADDRESSES should be set to 12` when building this example. Please change the IPv6 addresses number for LwIP network interface in menuconfig and rebuild the example again.

### CLI Command
Use Thread CLI command in device console
> Note: Thread Border Router is not initialized until the Controller has been commissioned and obtained an IP address.
```
matter esp ot_cli <command>
```


> Please refer to the [README in the parent folder](../README.md) for instructions.
