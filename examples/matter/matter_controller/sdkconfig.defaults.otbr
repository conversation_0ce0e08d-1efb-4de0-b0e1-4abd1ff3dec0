# ESP Thread Border Router DevKits use 4MB flash
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y

# SPIRAM
CONFIG_SPIRAM=y
CONFIG_SPIRAM_MODE_QUAD=y
CONFIG_SPIRAM_SPEED_80M=y
CONFIG_SPIRAM_MALLOC_ALWAYSINTERNAL=512
CONFIG_SPIRAM_MALLOC_RESERVE_INTERNAL=8192
CONFIG_SPIRAM_ALLOW_BSS_SEG_EXTERNAL_MEMORY=y
CONFIG_SPIRAM_TRY_ALLOCATE_WIFI_LWIP=y
CONFIG_ESP_ALLOW_BSS_SEG_EXTERNAL_MEMORY=y
CONFIG_BT_NIMBLE_MEM_ALLOC_MODE_EXTERNAL=y
CONFIG_ESP_MATTER_MEM_ALLOC_MODE_EXTERNAL=y
CONFIG_MBEDTLS_EXTERNAL_MEM_ALLOC=y

# Enable Openthread Border Router, but don't run matter over Thread
CONFIG_OPENTHREAD_ENABLED=y
CONFIG_OPENTHREAD_LOG_LEVEL_DYNAMIC=n
CONFIG_OPENTHREAD_LOG_LEVEL_NOTE=y
CONFIG_OPENTHREAD_BORDER_ROUTER=y
CONFIG_OPENTHREAD_SRP_CLIENT=n
CONFIG_OPENTHREAD_DNS_CLIENT=n
CONFIG_THREAD_NETWORK_COMMISSIONING_DRIVER=n

#LwIP config for OpenThread
CONFIG_LWIP_IPV6_AUTOCONFIG=y
CONFIG_LWIP_IPV6_NUM_ADDRESSES=8
CONFIG_LWIP_MULTICAST_PING=y
CONFIG_LWIP_IPV6_FORWARD=y
CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT=y
CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT=y
CONFIG_LWIP_NETIF_STATUS_CALLBACK=y

# mbedTLS
CONFIG_MBEDTLS_HARDWARE_AES=y
CONFIG_MBEDTLS_HARDWARE_MPI=y
CONFIG_MBEDTLS_HARDWARE_SHA=y
CONFIG_MBEDTLS_CMAC_C=y
CONFIG_MBEDTLS_KEY_EXCHANGE_ECJPAKE=y
CONFIG_MBEDTLS_ECJPAKE_C=y
CONFIG_MBEDTLS_SSL_PROTO_DTLS=y
CONFIG_MBEDTLS_HKDF_C=y

# MDNS platform
CONFIG_USE_MINIMAL_MDNS=n
CONFIG_ENABLE_EXTENDED_DISCOVERY=y
CONFIG_MDNS_MULTIPLE_INSTANCE=y

# Enable chip shell
CONFIG_ENABLE_CHIP_SHELL=y
CONFIG_ESP_MATTER_CONSOLE_TASK_STACK=4096

# Increase Stack size
CONFIG_CHIP_TASK_STACK_SIZE=10240
CONFIG_ESP_MAIN_TASK_STACK_SIZE=8192
CONFIG_THREAD_TASK_STACK_SIZE=8192

# Wi-Fi Settings
CONFIG_ENABLE_WIFI_STATION=y
CONFIG_ENABLE_WIFI_AP=n

# Enable Controller and disable commissioner
CONFIG_ESP_MATTER_CONTROLLER_ENABLE=y
CONFIG_ESP_MATTER_COMMISSIONER_ENABLE=n
CONFIG_CONTROLLER_CUSTOM_CLUSTER_ENABLE=y
CONFIG_USE_BLE_ONLY_FOR_COMMISSIONING=n

# Use a custom partition table
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions_br.csv"

# Disable chip test build
CONFIG_BUILD_CHIP_TESTS=n

# Disable OTA Requestor
CONFIG_ENABLE_OTA_REQUESTOR=n

# Use compact attribute storage mode
CONFIG_ESP_MATTER_NVS_USE_COMPACT_ATTR_STORAGE=y

# Disable route hook for matter since OTBR has already initialize the route hook
CONFIG_ENABLE_ROUTE_HOOK=n

CONFIG_OPENTHREAD_CLI=y

# Use USB Jtag Console
CONFIG_ESP_CONSOLE_USB_SERIAL_JTAG=y
