CONFIG_ESPTOOLPY_FLASHSIZE_8MB=y
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"

# mbedtls
CONFIG_MBEDTLS_DYNAMIC_BUFFER=y
CONFIG_MBEDTLS_DYNAMIC_FREE_CONFIG_DATA=y
CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN=y
CONFIG_MBEDTLS_HKDF_C=y

# For BLE Provisioning using NimBLE stack
CONFIG_BT_ENABLED=y
CONFIG_BT_NIMBLE_ENABLED=y

# Temporary Fix for Timer Overflows
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=3120

# For additional security on reset to factory
CONFIG_ESP_RMAKER_USER_ID_CHECK=y

# Application Rollback
CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE=y

# Use platform mdns
CONFIG_USE_MINIMAL_MDNS=n

# Enable Matter shell
CONFIG_ENABLE_CHIP_SHELL=y
CONFIG_ESP_MATTER_CONSOLE_TASK_STACK=4096

# Disable Matter server and enable Controller
CONFIG_ESP_MATTER_ENABLE_MATTER_SERVER=n
CONFIG_ENABLE_CHIP_CONTROLLER_BUILD=y
CONFIG_ESP_MATTER_CONTROLLER_ENABLE=y
CONFIG_ESP_MATTER_COMMISSIONER_ENABLE=n
CONFIG_ESP_MATTER_CONTROLLER_CUSTOM_CLUSTER_ENABLE=n

# Disable network commissioning driver
CONFIG_WIFI_NETWORK_COMMISSIONING_DRIVER=n
CONFIG_THREAD_NETWORK_COMMISSIONING_DRIVER=n

# Use custom credentials issuer
CONFIG_CUSTOM_OPERATIONAL_CREDS_ISSUER=y

# Disable Matter over SoftAP and BLE
CONFIG_ENABLE_WIFI_AP=n
CONFIG_ENABLE_CHIPOBLE=n
CONFIG_USE_BLE_ONLY_FOR_COMMISSIONING=n

# Matter Commissioning Window should be closed on starting
CONFIG_CHIP_ENABLE_PAIRING_AUTOSTART=n

# Increase IPv6 address Number
CONFIG_LWIP_IPV6_NUM_ADDRESSES=6

# Increase udp endpoints number
CONFIG_NUM_UDP_ENDPOINTS=16

# Enable lwIP route hooks
CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT=y
CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT=y
CONFIG_LWIP_IPV6_AUTOCONFIG=y

# Increase Main Task stack
CONFIG_ESP_MAIN_TASK_STACK_SIZE=8192

# Enable Controller REST APIs
CONFIG_RMAKER_REST_API_ENABLED=y

# Increase MQTT task stack size
CONFIG_MQTT_USE_CUSTOM_CONFIG=y
CONFIG_MQTT_TASK_STACK_SIZE=8192
