# Bluetooth
CONFIG_BT_ENABLED=y
CONFIG_BT_RELEASE_IRAM=y
CONFIG_BT_NIMBLE_ENABLED=y

## NimBLE Options
CONFIG_BT_NIMBLE_MAX_CONNECTIONS=1
CONFIG_BT_NIMBLE_MAX_BONDS=2
CONFIG_BT_NIMBLE_MAX_CCCDS=2
CONFIG_BT_NIMBLE_HOST_TASK_STACK_SIZE=3072
CONFIG_BT_NIMBLE_ROLE_CENTRAL=n
CONFIG_BT_NIMBLE_ROLE_OBSERVER=n
CONFIG_BT_NIMBLE_MSYS_1_BLOCK_COUNT=10
CONFIG_BT_NIMBLE_MSYS_1_BLOCK_SIZE=100
CONFIG_BT_NIMBLE_MSYS_2_BLOCK_COUNT=4
CONFIG_BT_NIMBLE_ACL_BUF_COUNT=5
CONFIG_BT_NIMBLE_HCI_EVT_HI_BUF_COUNT=5
CONFIG_BT_NIMBLE_HCI_EVT_LO_BUF_COUNT=3
CONFIG_BT_NIMBLE_GATT_MAX_PROCS=1
CONFIG_BT_NIMBLE_ENABLE_CONN_REATTEMPT=n
CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT=n
CONFIG_BT_NIMBLE_WHITELIST_SIZE=1
## Controller Options
CONFIG_BT_LE_CONTROLLER_TASK_STACK_SIZE=3072
CONFIG_BT_LE_LL_RESOLV_LIST_SIZE=1
CONFIG_BT_LE_LL_DUP_SCAN_LIST_COUNT=1

# SPI Configuration
CONFIG_SPI_MASTER_ISR_IN_IRAM=n
CONFIG_SPI_SLAVE_ISR_IN_IRAM=n

# Ethernet
CONFIG_ETH_USE_SPI_ETHERNET=n

# Event Loop Library
CONFIG_ESP_EVENT_POST_FROM_ISR=n

# Chip revision
CONFIG_ESP32C2_REV2_DEVELOPMENT=y

# ESP Ringbuf
CONFIG_RINGBUF_PLACE_FUNCTIONS_INTO_FLASH=y
CONFIG_RINGBUF_PLACE_ISR_FUNCTIONS_INTO_FLASH=y

# ESP System Settings
CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE=16
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=2048
CONFIG_ESP_MAIN_TASK_STACK_SIZE=3072

# Bypass a bug. Use 26M XTAL Freq
CONFIG_XTAL_FREQ_26=y

## Memory protection
CONFIG_ESP_SYSTEM_PMP_IDRAM_SPLIT=n

# High resolution timer (esp_timer)
CONFIG_ESP_TIMER_TASK_STACK_SIZE=2048

# Wi-Fi
CONFIG_ESP32_WIFI_SW_COEXIST_ENABLE=n
CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM=3
CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM=6
CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER_NUM=6
CONFIG_ESP32_WIFI_IRAM_OPT=n
CONFIG_ESP32_WIFI_RX_IRAM_OPT=n
CONFIG_ESP32_WIFI_ENABLE_WPA3_SAE=n
CONFIG_ESP32_WIFI_ENABLE_WPA3_OWE_STA=n
CONFIG_ESP_WIFI_STA_DISCONNECTED_PM_ENABLE=n

# FreeRTOS
## Kernel
CONFIG_FREERTOS_HZ=1000
CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=y
## Port
CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER=n
CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH=y
CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH=y

# Hardware Abstraction Layer (HAL) and Low Level (LL)
CONFIG_HAL_ASSERTION_DISABLE=y

# LWIP
CONFIG_LWIP_TCPIP_RECVMBOX_SIZE=16
CONFIG_LWIP_DHCPS=n
CONFIG_LWIP_IPV6_AUTOCONFIG=y
CONFIG_LWIP_MAX_ACTIVE_TCP=5
CONFIG_LWIP_MAX_LISTENING_TCP=5
CONFIG_LWIP_TCP_HIGH_SPEED_RETRANSMISSION=n
CONFIG_LWIP_TCP_SYNMAXRTX=12
CONFIG_LWIP_TCP_MSL=40000
CONFIG_LWIP_TCP_FIN_WAIT_TIMEOUT=16000
CONFIG_LWIP_TCP_SND_BUF_DEFAULT=4096
CONFIG_LWIP_TCP_WND_DEFAULT=2440
CONFIG_LWIP_TCP_OVERSIZE_QUARTER_MSS=y
CONFIG_LWIP_TCP_RTO_TIME=1500
CONFIG_LWIP_MAX_UDP_PCBS=8
CONFIG_LWIP_TCPIP_TASK_STACK_SIZE=2560
CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT=y
CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT=y

# mbedTLS
CONFIG_MBEDTLS_DYNAMIC_BUFFER=y
CONFIG_MBEDTLS_DYNAMIC_FREE_CONFIG_DATA=y
CONFIG_MBEDTLS_DYNAMIC_FREE_CA_CERT=y
CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE=n
CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN=y

# SPI Flash driver
CONFIG_SPI_FLASH_ROM_DRIVER_PATCH=n
CONFIG_SPI_FLASH_ROM_IMPL=y

# Websocket
CONFIG_WS_TRANSPORT=n

# Virtual file system
CONFIG_VFS_SUPPORT_DIR=n
CONFIG_VFS_SUPPORT_SELECT=n
CONFIG_VFS_SUPPORT_TERMIOS=n

# Wear Levelling
CONFIG_WL_SECTOR_SIZE_512=y
