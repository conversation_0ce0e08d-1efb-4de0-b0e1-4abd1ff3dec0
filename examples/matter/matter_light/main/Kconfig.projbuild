menu "Example Config"
    
    config EXAMPLE_USE_RAINMAKER_FABRIC
        bool "Use RainMaker plus Matter fabric solution"
        default y
        help
            Enable this option to make example run RainMaker plus Matter fabric solution. If want to use no fabric solution, disable it.

    config DYNAMIC_PASSCODE_COMMISSIONABLE_DATA_PROVIDER
        bool "Enable Dynamic Passcode Commissionable Data Provider"
        depends on !EXAMPLE_USE_RAINMAKER_FABRIC && CUSTOM_COMMISSIONABLE_DATA_PROVIDER
        default n

    config DYNAMIC_PASSCODE_PROVIDER_DISCRIMINATOR
        int "Discriminator in Dynamic Passcode Commissionable Data Provider"
        depends on DYNAMIC_PASSCODE_COMMISSIONABLE_DATA_PROVIDER
        default 3840
        range 0 4095
        help
            Fixed discriminator in custom dynamic passcode commissionable data provider

    config DYNAMIC_PASSCODE_PROVIDER_ITERATIONS
        int "Iterations in Dynamic Passcode Commissionable Data Provider"
        depends on DYNAMIC_PASSCODE_COMMISSI<PERSON><PERSON>LE_DATA_PROVIDER
        default 10000
        range 1000 100000
        help
            Fixed iterations in custom dynamic passcode commissionable data provider

    config DYNAMIC_PASSCODE_PROVIDER_SALT_BASE64
        string "Base64-Encoded Salt in Dynamic Passcode Commissionable Data Provider"
        depends on DYNAMIC_PASSCODE_COMMISSIONABLE_DATA_PROVIDER
        default "0NHS09TV1tfY2drb3N3e36ChoqOkpaanqKmqq6ytrq8="
        help
            Fixed salt in custom dynamic passcode commissionable data provider. It should be a Base64-Encoded string.

endmenu
