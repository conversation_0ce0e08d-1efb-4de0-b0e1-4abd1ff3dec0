CONFIG_IDF_TARGET="esp32c6"

CONFIG_ESPTOOLPY_MONITOR_BAUD=115200
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y

# Use a custom partition table
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_OFFSET=0xC000
CONFIG_PARTITION_TABLE_MD5=y

# Enable chip shell
CONFIG_ENABLE_CHIP_SHELL=y

# mbedtls
CONFIG_MBEDTLS_DYNAMIC_BUFFER=y
CONFIG_MBEDTLS_DYNAMIC_FREE_PEER_CERT=y
CONFIG_MBEDTLS_DYNAMIC_FREE_CONFIG_DATA=y

# Temporary Fix for Timer Overflows
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=3120

# Button
CONFIG_BUTTON_PERIOD_TIME_MS=20
CONFIG_BUTTON_LONG_PRESS_TIME_MS=5000

# Disable softap by default
CONFIG_ESP_WIFI_SOFTAP_SUPPORT=n

# ESP RainMaker
CONFIG_ESP_RMAKER_USER_ID_CHECK=y
CONFIG_ESP_RMAKER_NO_CLAIM=y
CONFIG_ESP_RMAKER_USE_ESP_SECURE_CERT_MGR=y
CONFIG_ESP_RMAKER_READ_NODE_ID_FROM_CERT_CN=y
CONFIG_ESP_RMAKER_DISABLE_USER_MAPPING_PROV=y
CONFIG_ESP_RMAKER_NETWORK_OVER_THREAD=y

# ESP Matter
CONFIG_CHIP_FACTORY_NAMESPACE_PARTITION_LABEL="fctry"
CONFIG_ENABLE_ESP32_FACTORY_DATA_PROVIDER=y
CONFIG_ENABLE_ESP32_DEVICE_INSTANCE_INFO_PROVIDER=y
CONFIG_ENABLE_ESP32_DEVICE_INFO_PROVIDER=y
CONFIG_SEC_CERT_DAC_PROVIDER=y
CONFIG_DEVICE_VENDOR_ID=0x131B
CONFIG_DEVICE_PRODUCT_ID=0x2

CONFIG_ESP_SECURE_CERT_DS_PERIPHERAL=n

# Enable HKDF in mbedtls
CONFIG_MBEDTLS_HKDF_C=y

# Use compact attribute storage mode
CONFIG_ESP_MATTER_NVS_USE_COMPACT_ATTR_STORAGE=y

# libsodium
CONFIG_LIBSODIUM_USE_MBEDTLS_SHA=y

# NIMBLE
CONFIG_BT_ENABLED=y
CONFIG_BT_NIMBLE_ENABLED=y
CONFIG_BT_NIMBLE_EXT_ADV=n
CONFIG_BT_NIMBLE_HCI_EVT_BUF_SIZE=70

# Disable BT connection re-attempts
CONFIG_BT_NIMBLE_ENABLE_CONN_REATTEMPT=n
CONFIG_BTDM_CTRL_MODE_BLE_ONLY=y

# FreeRTOS should use legacy API
CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY=y

# Enable OpenThread
CONFIG_OPENTHREAD_ENABLED=y
CONFIG_OPENTHREAD_SRP_CLIENT=y
CONFIG_OPENTHREAD_DNS_CLIENT=y
CONFIG_OPENTHREAD_LOG_LEVEL_DYNAMIC=n
CONFIG_OPENTHREAD_LOG_LEVEL_NOTE=y
CONFIG_OPENTHREAD_CLI=n
CONFIG_OPENTHREAD_DNS64_CLIENT=y


# Disable lwip ipv6 autoconfig
CONFIG_LWIP_IPV6_AUTOCONFIG=n

# Use a custom partition table
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"

# LwIP config for OpenThread
CONFIG_LWIP_IPV6_NUM_ADDRESSES=8
CONFIG_LWIP_MULTICAST_PING=y
CONFIG_LWIP_IPV6_AUTOCONFIG=y

# MDNS platform
CONFIG_USE_MINIMAL_MDNS=n
CONFIG_ENABLE_EXTENDED_DISCOVERY=y

# Disable STA and AP for ESP32C6
CONFIG_ENABLE_WIFI_STATION=n
CONFIG_ENABLE_WIFI_AP=n

# Disable ECDSA peripheral
CONFIG_USE_ESP32_ECDSA_PERIPHERAL=n
