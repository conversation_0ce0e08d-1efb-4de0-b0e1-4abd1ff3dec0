# Default to 921600 baud when flashing and monitoring device
CONFIG_ESPTOOLPY_BAUD_921600B=y
CONFIG_ESPTOOLPY_BAUD=921600
CONFIG_ESPTOOLPY_COMPRESSED=y
CONFIG_ESPTOOLPY_MONITOR_BAUD_115200B=y
CONFIG_ESPTOOLPY_MONITOR_BAUD=115200
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE=y

# Enable BT
CONFIG_BT_ENABLED=y
CONFIG_BT_NIMBLE_ENABLED=y

# Disable BT connection re-attempts
CONFIG_BT_NIMBLE_ENABLE_CONN_REATTEMPT=n
CONFIG_BTDM_CTRL_MODE_BLE_ONLY=y

# Enable lwip ipv6 autoconfig
CONFIG_LWIP_IPV6_AUTOCONFIG=y

# Use a custom partition table
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_OFFSET=0xC000
CONFIG_PARTITION_TABLE_MD5=y

# Enable chip shell
CONFIG_ENABLE_CHIP_SHELL=y

# mbedtls
CONFIG_MBEDTLS_DYNAMIC_BUFFER=y
CONFIG_MBEDTLS_DYNAMIC_FREE_PEER_CERT=y
CONFIG_MBEDTLS_DYNAMIC_FREE_CONFIG_DATA=y
CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE=n

# Temporary Fix for Timer Overflows
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=3120

# Enable lwIP route hooks
CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT=y
CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT=y

# Button
CONFIG_BUTTON_PERIOD_TIME_MS=20
CONFIG_BUTTON_LONG_PRESS_TIME_MS=5000

# Disable softap by default
CONFIG_ESP_WIFI_SOFTAP_SUPPORT=n

# ESP RainMaker
CONFIG_ESP_RMAKER_USER_ID_CHECK=y
CONFIG_ESP_RMAKER_NO_CLAIM=y
CONFIG_ESP_RMAKER_USE_ESP_SECURE_CERT_MGR=y
CONFIG_ESP_RMAKER_READ_NODE_ID_FROM_CERT_CN=y
CONFIG_ESP_RMAKER_DISABLE_USER_MAPPING_PROV=y

# ESP Matter
CONFIG_CHIP_FACTORY_NAMESPACE_PARTITION_LABEL="fctry"
CONFIG_ENABLE_ESP32_FACTORY_DATA_PROVIDER=y
CONFIG_ENABLE_ESP32_DEVICE_INSTANCE_INFO_PROVIDER=y
CONFIG_ENABLE_ESP32_DEVICE_INFO_PROVIDER=y
CONFIG_SEC_CERT_DAC_PROVIDER=y
CONFIG_DEVICE_VENDOR_ID=0x131B
CONFIG_DEVICE_PRODUCT_ID=0x2

CONFIG_ESP_SECURE_CERT_DS_PERIPHERAL=n

# Enable HKDF in mbedtls
CONFIG_MBEDTLS_HKDF_C=y

# Use compact attribute storage mode
CONFIG_ESP_MATTER_NVS_USE_COMPACT_ATTR_STORAGE=y

# Increase LwIP IPv6 address number to 6 (MAX_FABRIC + 1)
# unique local addresses for fabrics(MAX_FABRIC), a link local address(1)
CONFIG_LWIP_IPV6_NUM_ADDRESSES=6

# If ESP-Insights is enabled, we need MQTT transport selected
# Takes out manual efforts to enable this option
CONFIG_ESP_INSIGHTS_TRANSPORT_MQTT=y

# Flash optimization
CONFIG_COMPILER_OPTIMIZATION_SIZE=y
CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT=y
CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT=y

# DRAM optimization
CONFIG_ENABLE_CHIP_SHELL=n
CONFIG_ESP_MATTER_MAX_DYNAMIC_ENDPOINT_COUNT=2