# ESP RainMaker
CONFIG_ESP_RMAKER_LOCAL_CTRL_AUTO_ENABLE=n
CONFIG_ESP_RMAKER_WORK_QUEUE_TASK_STACK=5120

# Wi-Fi
CONFIG_ESP32_WIFI_SW_COEXIST_ENABLE=n
CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM=4
CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM=8
CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER_NUM=8
CONFIG_ESP32_WIFI_IRAM_OPT=n
CONFIG_ESP32_WIFI_RX_IRAM_OPT=n
CONFIG_ESP32_WIFI_ENABLE_WPA3_SAE=n
CONFIG_ESP32_WIFI_ENABLE_WPA3_OWE_STA=n
CONFIG_ESP_WIFI_STA_DISCONNECTED_PM_ENABLE=n
CONFIG_ESP_WIFI_SOFTAP_SUPPORT=n

# LWIP
CONFIG_LWIP_TCPIP_RECVMBOX_SIZE=16
CONFIG_LWIP_DHCPS=n
CONFIG_LWIP_TCP_OVERSIZE_QUARTER_MSS=y
CONFIG_LWIP_TCP_RTO_TIME=1500
CONFIG_LWIP_MAX_UDP_PCBS=8
CONFIG_LWIP_TCPIP_TASK_STACK_SIZE=2560

# mbedTLS
CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN=y
CONFIG_MBEDTLS_TLS_CLIENT_ONLY=y
CONFIG_MBEDTLS_ERROR_STRINGS=n

# SPI Flash driver
CONFIG_SPI_FLASH_ROM_DRIVER_PATCH=n
CONFIG_SPI_FLASH_ROM_IMPL=y

# Websocket
CONFIG_WS_TRANSPORT=n

# Virtual file system
CONFIG_VFS_SUPPORT_DIR=n
CONFIG_VFS_SUPPORT_SELECT=n
CONFIG_VFS_SUPPORT_TERMIOS=n

# Wear Levelling
CONFIG_WL_SECTOR_SIZE_512=y

# Memory Optimization
CONFIG_ESP_WIFI_IRAM_OPT=n
CONFIG_ESP_WIFI_RX_IRAM_OPT=n
CONFIG_ESP_MATTER_MAX_DEVICE_TYPE_COUNT=2
CONFIG_ESP_MATTER_BINDING_TABLE_SIZE=1
CONFIG_EVENT_LOGGING_CRIT_BUFFER_SIZE=512
CONFIG_EVENT_LOGGING_INFO_BUFFER_SIZE=256
CONFIG_EVENT_LOGGING_DEBUG_BUFFER_SIZE=256
CONFIG_MAX_EXCHANGE_CONTEXTS=5
CONFIG_MAX_BINDINGS=2
CONFIG_MAX_PEER_NODES=8
CONFIG_NUM_TCP_ENDPOINTS=1
CONFIG_NUM_UDP_ENDPOINTS=6
CONFIG_NEWLIB_NANO_FORMAT=y
