# Default to 921600 baud when flashing and monitoring device
CONFIG_ESPTO<PERSON>PY_BAUD_921600B=y
CONFIG_ESPTOOLPY_BAUD=921600
CONFIG_ESPTOOLPY_COMPRESSED=y
CONFIG_ESPTOOLPY_MONITOR_BAUD_115200B=y
CONFIG_ESPTOOLPY_MONITOR_BAUD=115200
CONFIG_ESPTOOLPY_FLASHSIZE_4MB=y
CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE=y

#enable BT
CONFIG_BT_ENABLED=y
CONFIG_BT_NIMBLE_ENABLED=y
CONFIG_BT_NIMBLE_ROLE_CENTRAL=n
CONFIG_BT_NIMBLE_ROLE_OBSERVER=n
CONFIG_BT_NIMBLE_LOG_LEVEL_WARNING=y

# Enable Extended advertisement of NIMBLE
CONFIG_BT_NIMBLE_EXT_ADV=y
CONFIG_BT_NIMBLE_MAX_EXT_ADV_INSTANCES=2

#disable BT connection reattempt
CONFIG_BT_NIMBLE_ENABLE_CONN_REATTEMPT=n

# Enable lwip ipv6 autoconfig
CONFIG_LWIP_IPV6_AUTOCONFIG=y

# Use a custom partition table
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_FILENAME="partitions.csv"
CONFIG_PARTITION_TABLE_OFFSET=0xC000
CONFIG_PARTITION_TABLE_MD5=y

# Enable chip shell
CONFIG_ENABLE_CHIP_SHELL=y

# Temporary Fix for Timer Overflows
CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH=3120

# Enable lwIP route hooks
CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT=y
CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT=y

# ESP RainMaker
CONFIG_ESP_RMAKER_USER_ID_CHECK=y
CONFIG_ESP_RMAKER_NO_CLAIM=y
CONFIG_ESP_RMAKER_USE_ESP_SECURE_CERT_MGR=y
CONFIG_ESP_RMAKER_READ_NODE_ID_FROM_CERT_CN=y
CONFIG_ESP_RMAKER_DISABLE_USER_MAPPING_PROV=n
CONFIG_ESP_RMAKER_LOCAL_CTRL_FEATURE_ENABLE=y
CONFIG_ESP_RMAKER_LOCAL_CTRL_AUTO_ENABLE=n
CONFIG_ESP_RMAKER_LOCAL_CTRL_SECURITY_0=y
CONFIG_ESP_RMAKER_WORK_QUEUE_TASK_STACK=5120

# ESP Matter
CONFIG_CHIP_FACTORY_NAMESPACE_PARTITION_LABEL="fctry"
CONFIG_ENABLE_ESP32_FACTORY_DATA_PROVIDER=y
CONFIG_ENABLE_ESP32_DEVICE_INSTANCE_INFO_PROVIDER=y
CONFIG_ENABLE_ESP32_DEVICE_INFO_PROVIDER=y
CONFIG_SEC_CERT_DAC_PROVIDER=y
CONFIG_DEVICE_VENDOR_ID=0x131B
CONFIG_DEVICE_PRODUCT_ID=0x2
CONFIG_USE_MINIMAL_MDNS=n

CONFIG_ESP_SECURE_CERT_DS_PERIPHERAL=n

# Enable HKDF in mbedtls
CONFIG_MBEDTLS_HKDF_C=y

# Use compact attribute storage mode
CONFIG_ESP_MATTER_NVS_USE_COMPACT_ATTR_STORAGE=y

# Increase LwIP IPv6 address number to 6 (MAX_FABRIC + 1)
# unique local addresses for fabrics(MAX_FABRIC), a link local address(1)
CONFIG_LWIP_IPV6_NUM_ADDRESSES=6

# If ESP-Insights is enabled, we need MQTT transport selected
# Takes out manual efforts to enable this option
CONFIG_ESP_INSIGHTS_TRANSPORT_MQTT=y

# SPI Configuration
CONFIG_SPI_MASTER_ISR_IN_IRAM=n
CONFIG_SPI_SLAVE_ISR_IN_IRAM=n

# Ethernet
CONFIG_ETH_USE_SPI_ETHERNET=n

# Event Loop Library
CONFIG_ESP_EVENT_POST_FROM_ISR=n

# ESP Ringbuf
CONFIG_RINGBUF_PLACE_FUNCTIONS_INTO_FLASH=y
CONFIG_RINGBUF_PLACE_ISR_FUNCTIONS_INTO_FLASH=y

# ESP System Settings
CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE=16
CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE=2048
CONFIG_ESP_MAIN_TASK_STACK_SIZE=3072

# High resolution timer (esp_timer)
CONFIG_ESP_TIMER_TASK_STACK_SIZE=2048

# Wi-Fi
CONFIG_ESP32_WIFI_SW_COEXIST_ENABLE=n
CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM=4
CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM=8
CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER_NUM=8
CONFIG_ESP32_WIFI_IRAM_OPT=n
CONFIG_ESP32_WIFI_RX_IRAM_OPT=n
CONFIG_ESP32_WIFI_ENABLE_WPA3_SAE=n
CONFIG_ESP32_WIFI_ENABLE_WPA3_OWE_STA=n
CONFIG_ESP_WIFI_STA_DISCONNECTED_PM_ENABLE=n
CONFIG_ESP_WIFI_SOFTAP_SUPPORT=n

# FreeRTOS
## Kernel
CONFIG_FREERTOS_HZ=1000
CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS=y
## Port
CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER=n
CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH=y
CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH=y

# Hardware Abstraction Layer (HAL) and Low Level (LL)
CONFIG_HAL_ASSERTION_DISABLE=y

# LWIP
CONFIG_LWIP_TCPIP_RECVMBOX_SIZE=16
CONFIG_LWIP_DHCPS=n
CONFIG_LWIP_IPV6_AUTOCONFIG=y
CONFIG_LWIP_TCP_OVERSIZE_QUARTER_MSS=y
CONFIG_LWIP_TCP_RTO_TIME=1500
CONFIG_LWIP_MAX_UDP_PCBS=8
CONFIG_LWIP_TCPIP_TASK_STACK_SIZE=2560
CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT=y
CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT=y

# mbedTLS
CONFIG_MBEDTLS_DYNAMIC_BUFFER=y
CONFIG_MBEDTLS_DYNAMIC_FREE_CONFIG_DATA=y
CONFIG_MBEDTLS_DYNAMIC_FREE_CA_CERT=y
CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE=n
CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN=y
CONFIG_MBEDTLS_ERROR_STRINGS=n

# SPI Flash driver
CONFIG_SPI_FLASH_ROM_DRIVER_PATCH=n
CONFIG_SPI_FLASH_ROM_IMPL=y

# Websocket
CONFIG_WS_TRANSPORT=n

# Virtual file system
CONFIG_VFS_SUPPORT_DIR=n
CONFIG_VFS_SUPPORT_SELECT=n
CONFIG_VFS_SUPPORT_TERMIOS=n

# Wear Levelling
CONFIG_WL_SECTOR_SIZE_512=y

# Memory Optimization
CONFIG_ESP_WIFI_IRAM_OPT=n
CONFIG_ESP_WIFI_RX_IRAM_OPT=n
CONFIG_ESP_MATTER_MAX_DEVICE_TYPE_COUNT=2
CONFIG_ESP_MATTER_BINDING_TABLE_SIZE=1
CONFIG_EVENT_LOGGING_CRIT_BUFFER_SIZE=512
CONFIG_EVENT_LOGGING_INFO_BUFFER_SIZE=256
CONFIG_EVENT_LOGGING_DEBUG_BUFFER_SIZE=256
CONFIG_MAX_EXCHANGE_CONTEXTS=5
CONFIG_MAX_BINDINGS=2
CONFIG_MAX_PEER_NODES=8
CONFIG_NUM_TCP_ENDPOINTS=1
CONFIG_NUM_UDP_ENDPOINTS=6
CONFIG_NEWLIB_NANO_FORMAT=y

# Example Config
CONFIG_EXAMPLE_USE_RAINMAKER_FABRIC=n
