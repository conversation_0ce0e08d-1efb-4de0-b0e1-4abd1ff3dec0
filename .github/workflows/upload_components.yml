name: Push components to Espressif Component Service

on:
  push:
    branches:
      - master

jobs:
  upload_components:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Upload ESP RainMaker components to Component Registry
        uses: espressif/upload-components-ci-action@v1
        with:
          directories: >
            components/esp_schedule;
            components/esp_rainmaker;
          namespace: "espressif"
          api_token: ${{ secrets.IDF_COMPONENT_API_TOKEN }}
