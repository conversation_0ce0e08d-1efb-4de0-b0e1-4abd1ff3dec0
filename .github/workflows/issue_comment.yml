name: Sync issue comments to JIRA

# This workflow will be triggered when new issue comment is created (including PR comments)
on: issue_comment

jobs:
  sync_issue_comments_to_jira:
    name: Sync Issue Comments to <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@master
      - name: Sync issue comments to JIRA
        uses: espressif/github-actions/sync_issues_to_jira@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          JIRA_PASS: ${{ secrets.JIRA_PASS }}
          JIRA_PROJECT: MEGH
          JIRA_COMPONENT: GitHub
          JIRA_URL: ${{ secrets.JIRA_URL }}
          JIRA_USER: ${{ secrets.JIRA_USER }}
