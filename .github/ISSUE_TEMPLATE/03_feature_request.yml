name: Feature request
description: Suggest an idea for this project.
labels: ['Type: Feature Request']
body:
  - type: markdown
    attributes:
      value: We welcome any ideas or feature requests! It’s helpful if you can explain exactly why the feature would be useful.
  - type: textarea
    id: problem-related
    attributes:
      label: Is your feature request related to a problem?
      description: Please provide a clear and concise description of what the problem is.
      placeholder: ex. I'm always frustrated when ...
  - type: textarea
    id: solution
    attributes:
      label: Describe the solution you'd like.
      description: Please provide a clear and concise description of what you want to happen.
      placeholder: ex. When using the Rainmaker app ...
  - type: textarea
    id: alternatives
    attributes:
      label: Describe alternatives you've considered.
      description: Please provide a clear and concise description of any alternative solutions or features you've considered.
      placeholder: ex. Choosing other approach wouldn't work, because ...
  - type: textarea
    id: context
    attributes:
      label: Additional context.
      description: Please add any other context or screenshots about the feature request here.
      placeholder: ex. This would work only when ...
