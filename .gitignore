# Prerequisites
*.d

# Object files
*.o
*.ko
*.obj
*.elf
*.pyc

# Linker output
*.ilk
*.map
*.exp

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Executables
*.exe
*.out
*.app
*.i*86
*.x86_64
*.hex

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Kernel Module Compile Results
*.mod*
*.cmd
.tmp_versions/
modules.order
Module.symvers
Mkfile.old
dkms.conf

# emacs
.dir-locals.el

# emacs temp file suffixes
*~
.#*
\#*#

# eclipse setting
.settings

# MacOS directory files
.DS_Store

# Application project files
**/sdkconfig
**/sdkconfig.old
**/build
**/cloud_cfg/*.key
**/cloud_cfg/*.crt
**/cloud_cfg/*.info

# node_modules
docs/docusaurus/website/node_modules

# cli logs
**/logs

# IDF package manager
**/managed_components/
*.lock

# Patch files
*.patch

# Failed patch files
*.rej
*.orig

